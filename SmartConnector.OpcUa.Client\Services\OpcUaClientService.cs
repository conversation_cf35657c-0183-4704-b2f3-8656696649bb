using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace SmartConnector.OpcUa.Client.Services
{
    /// <summary>
    /// OPC UA client background service
    /// </summary>
    public class OpcUaClientService : BackgroundService
    {
        private readonly ILogger<OpcUaClientService> _logger;
        private readonly IOpcUaClient _opcUaClient;
        private readonly OpcUaClientServiceConfiguration _config;
        private readonly Timer _healthCheckTimer;
        private readonly List<OpcUaDataSubscription> _dataSubscriptions = new();
        private readonly Dictionary<string, object?> _lastValues = new();

        public event EventHandler<OpcUaDataChangedEventArgs>? DataChanged;
        public event EventHandler<OpcUaConnectionChangedEventArgs>? ConnectionChanged;

        public OpcUaClientService(
            IOpcUaClient opcUaClient,
            IOptions<OpcUaClientServiceConfiguration> config,
            ILogger<OpcUaClientService> logger)
        {
            _opcUaClient = opcUaClient ?? throw new ArgumentNullException(nameof(opcUaClient));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _healthCheckTimer = new Timer(HealthCheckCallback, null, Timeout.Infinite, Timeout.Infinite);

            // Subscribe to client events
            _opcUaClient.ConnectionStatusChanged += OnConnectionStatusChanged;
            _opcUaClient.DataChanged += OnDataChanged;
            _opcUaClient.EventReceived += OnEventReceived;
            _opcUaClient.ErrorOccurred += OnErrorOccurred;
        }

        /// <summary>
        /// Start the service
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("OPC UA Client Service starting...");

            try
            {
                // Connect to server
                var connectResult = await _opcUaClient.ConnectAsync();
                if (!connectResult.IsSuccess)
                {
                    _logger.LogError("Failed to connect to OPC UA server: {Message}", connectResult.Message);
                    return;
                }

                _logger.LogInformation("Successfully connected to OPC UA server");

                // Setup subscriptions
                await SetupSubscriptionsAsync();

                // Start health check timer
                if (_config.HealthCheckInterval > 0)
                {
                    _healthCheckTimer.Change(TimeSpan.FromMilliseconds(_config.HealthCheckInterval), 
                        TimeSpan.FromMilliseconds(_config.HealthCheckInterval));
                }

                // Keep service running
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OPC UA Client Service");
            }
            finally
            {
                await CleanupAsync();
            }
        }

        /// <summary>
        /// Setup data subscriptions
        /// </summary>
        private async Task SetupSubscriptionsAsync()
        {
            try
            {
                foreach (var subscription in _config.DataSubscriptions)
                {
                    await CreateDataSubscriptionAsync(subscription);
                }

                _logger.LogInformation("Setup {Count} data subscriptions", _config.DataSubscriptions.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting up subscriptions");
            }
        }

        /// <summary>
        /// Create data subscription
        /// </summary>
        private async Task CreateDataSubscriptionAsync(OpcUaDataSubscription subscription)
        {
            try
            {
                var subscriptionSettings = new OpcUaSubscriptionSettings
                {
                    DisplayName = subscription.Name,
                    PublishingInterval = subscription.PublishingInterval,
                    MaxNotificationsPerPublish = subscription.MaxNotificationsPerPublish
                };

                var createResult = await _opcUaClient.CreateSubscriptionAsync(subscriptionSettings);
                if (!createResult.IsSuccess || createResult.Data == 0)
                {
                    _logger.LogError("Failed to create subscription '{Name}': {Message}", subscription.Name, createResult.Message);
                    return;
                }

                var subscriptionId = createResult.Data;
                subscription.SubscriptionId = subscriptionId;

                // Add monitored items
                var monitoredItems = subscription.MonitoredItems.Select(item => new OpcUaMonitoredItem
                {
                    ClientHandle = (uint)item.GetHashCode(),
                    NodeId = item.NodeId,
                    DisplayName = item.DisplayName,
                    SamplingInterval = item.SamplingInterval,
                    QueueSize = item.QueueSize,
                    MonitoringMode = item.MonitoringMode
                }).ToList();

                var addItemsResult = await _opcUaClient.AddMonitoredItemsAsync(subscriptionId, monitoredItems);
                if (!addItemsResult.IsSuccess)
                {
                    _logger.LogError("Failed to add monitored items to subscription '{Name}': {Message}", 
                        subscription.Name, addItemsResult.Message);
                    return;
                }

                _dataSubscriptions.Add(subscription);
                _logger.LogDebug("Created subscription '{Name}' with {ItemCount} monitored items", 
                    subscription.Name, monitoredItems.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating data subscription '{Name}'", subscription.Name);
            }
        }

        /// <summary>
        /// Health check timer callback
        /// </summary>
        private async void HealthCheckCallback(object? state)
        {
            try
            {
                if (!_opcUaClient.IsConnected)
                {
                    _logger.LogWarning("OPC UA client is not connected, attempting reconnect...");
                    
                    var reconnectResult = await _opcUaClient.ReconnectAsync();
                    if (reconnectResult.IsSuccess)
                    {
                        _logger.LogInformation("Successfully reconnected to OPC UA server");
                        await SetupSubscriptionsAsync();
                    }
                    else
                    {
                        _logger.LogError("Failed to reconnect: {Message}", reconnectResult.Message);
                    }
                }
                else
                {
                    // Perform health check by reading a test node
                    if (_config.HealthCheckNodeId != null)
                    {
                        var readResult = await _opcUaClient.ReadAsync(_config.HealthCheckNodeId);
                        if (!readResult.IsSuccess)
                        {
                            _logger.LogWarning("Health check read failed: {Message}", readResult.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during health check");
            }
        }

        /// <summary>
        /// Connection status changed handler
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, OpcUaConnectionEventArgs e)
        {
            _logger.LogInformation("OPC UA connection status changed: {IsConnected}", e.IsConnected);
            
            if (!e.IsConnected)
            {
                _dataSubscriptions.Clear();
            }

            ConnectionChanged?.Invoke(this, new OpcUaConnectionChangedEventArgs
            {
                IsConnected = e.IsConnected,
                Reason = e.Reason,
                Timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Data changed handler
        /// </summary>
        private void OnDataChanged(object? sender, OpcUaNotificationEventArgs e)
        {
            try
            {
                var changedItems = new List<OpcUaDataChangeItem>();

                foreach (var dataChange in e.DataChanges)
                {
                    var key = dataChange.NodeId.ToString();
                    var previousValue = _lastValues.ContainsKey(key) ? _lastValues[key] : null;
                    
                    _lastValues[key] = dataChange.Value;

                    // Find subscription info
                    var subscription = _dataSubscriptions.FirstOrDefault(s => s.SubscriptionId == e.SubscriptionId);
                    var monitoredItem = subscription?.MonitoredItems.FirstOrDefault(mi => 
                        mi.NodeId.Equals(dataChange.NodeId));

                    changedItems.Add(new OpcUaDataChangeItem
                    {
                        NodeId = dataChange.NodeId,
                        DisplayName = monitoredItem?.DisplayName ?? dataChange.DisplayName ?? dataChange.NodeId.ToString(),
                        Value = dataChange.Value,
                        PreviousValue = previousValue,
                        StatusCode = dataChange.StatusCode,
                        SourceTimestamp = dataChange.SourceTimestamp,
                        ServerTimestamp = dataChange.ServerTimestamp,
                        SubscriptionName = subscription?.Name ?? "Unknown"
                    });
                }

                if (changedItems.Count > 0)
                {
                    DataChanged?.Invoke(this, new OpcUaDataChangedEventArgs
                    {
                        ChangedItems = changedItems,
                        SubscriptionId = e.SubscriptionId,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing data change notification");
            }
        }

        /// <summary>
        /// Event received handler
        /// </summary>
        private void OnEventReceived(object? sender, OpcUaEventNotificationArgs e)
        {
            _logger.LogInformation("OPC UA event received: {Message} (Severity: {Severity})", e.Message, e.Severity);
        }

        /// <summary>
        /// Error occurred handler
        /// </summary>
        private void OnErrorOccurred(object? sender, OpcUaErrorEventArgs e)
        {
            _logger.LogError(e.Exception, "OPC UA error occurred: {Message}", e.Message);
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        private async Task CleanupAsync()
        {
            try
            {
                _healthCheckTimer.Change(Timeout.Infinite, Timeout.Infinite);

                foreach (var subscription in _dataSubscriptions)
                {
                    if (subscription.SubscriptionId.HasValue)
                    {
                        await _opcUaClient.DeleteSubscriptionAsync(subscription.SubscriptionId.Value);
                    }
                }

                await _opcUaClient.DisconnectAsync();
                
                _logger.LogInformation("OPC UA Client Service cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cleanup");
            }
        }

        /// <summary>
        /// Stop the service
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("OPC UA Client Service stopping...");
            
            await CleanupAsync();
            
            await base.StopAsync(cancellationToken);
            
            _logger.LogInformation("OPC UA Client Service stopped");
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public override void Dispose()
        {
            _healthCheckTimer?.Dispose();
            _opcUaClient?.Dispose();
            base.Dispose();
        }
    }

    /// <summary>
    /// OPC UA client service configuration
    /// </summary>
    public class OpcUaClientServiceConfiguration
    {
        public int HealthCheckInterval { get; set; } = 30000; // 30 seconds
        public Opc.Ua.NodeId? HealthCheckNodeId { get; set; }
        public List<OpcUaDataSubscription> DataSubscriptions { get; set; } = new();
        public bool AutoReconnect { get; set; } = true;
        public int ReconnectDelay { get; set; } = 5000;
        public int MaxReconnectAttempts { get; set; } = 0; // 0 = infinite
    }

    /// <summary>
    /// OPC UA data subscription configuration
    /// </summary>
    public class OpcUaDataSubscription
    {
        public string Name { get; set; } = string.Empty;
        public double PublishingInterval { get; set; } = 1000;
        public uint MaxNotificationsPerPublish { get; set; } = 1000;
        public List<OpcUaMonitoredItemConfig> MonitoredItems { get; set; } = new();
        public uint? SubscriptionId { get; set; }
    }

    /// <summary>
    /// OPC UA monitored item configuration
    /// </summary>
    public class OpcUaMonitoredItemConfig
    {
        public Opc.Ua.NodeId NodeId { get; set; } = Opc.Ua.NodeId.Null;
        public string? DisplayName { get; set; }
        public double SamplingInterval { get; set; } = 1000;
        public uint QueueSize { get; set; } = 1;
        public Opc.Ua.MonitoringMode MonitoringMode { get; set; } = Opc.Ua.MonitoringMode.Reporting;
    }

    /// <summary>
    /// OPC UA data changed event arguments
    /// </summary>
    public class OpcUaDataChangedEventArgs : EventArgs
    {
        public List<OpcUaDataChangeItem> ChangedItems { get; set; } = new();
        public uint SubscriptionId { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// OPC UA data change item
    /// </summary>
    public class OpcUaDataChangeItem
    {
        public Opc.Ua.NodeId NodeId { get; set; } = Opc.Ua.NodeId.Null;
        public string DisplayName { get; set; } = string.Empty;
        public object? Value { get; set; }
        public object? PreviousValue { get; set; }
        public Opc.Ua.StatusCode StatusCode { get; set; }
        public DateTime SourceTimestamp { get; set; }
        public DateTime ServerTimestamp { get; set; }
        public string SubscriptionName { get; set; } = string.Empty;
    }

    /// <summary>
    /// OPC UA connection changed event arguments
    /// </summary>
    public class OpcUaConnectionChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string? Reason { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
