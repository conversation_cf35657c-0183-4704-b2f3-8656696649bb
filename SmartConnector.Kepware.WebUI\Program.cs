using SmartConnector.Kepware.WebUI.Services;
using SmartConnector.Kepware.WebUI.Hubs;
using SmartConnector.Kepware.WebUI.Models;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/smartconnector-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();

// Configure Kepware connection
var kepwareConfig = new KepwareConfiguration();
builder.Configuration.GetSection("Kepware").Bind(kepwareConfig);
builder.Services.AddSingleton(kepwareConfig);

// Register services
builder.Services.AddHttpClient<KepwareConnector>();
builder.Services.AddSingleton<KepwareConnector>();
builder.Services.AddSingleton<KepwareDataService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseAuthorization();

// Configure routes
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Configure SignalR hub
app.MapHub<KepwareHub>("/kepwareHub");

// Start Kepware data service
var kepwareDataService = app.Services.GetRequiredService<KepwareDataService>();
await kepwareDataService.StartAsync();

Log.Information("SmartConnector Kepware WebUI started");

app.Run();
