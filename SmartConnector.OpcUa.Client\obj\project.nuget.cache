{"version": 2, "dgSpecHash": "rJkdz3wz6+E=", "success": true, "projectFilePath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\7.0.0\\microsoft.extensions.configuration.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\7.0.0\\microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\7.0.3\\microsoft.extensions.configuration.binder.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\7.0.0\\microsoft.extensions.configuration.commandline.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\7.0.0\\microsoft.extensions.configuration.environmentvariables.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\7.0.0\\microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\7.0.0\\microsoft.extensions.configuration.json.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\7.0.0\\microsoft.extensions.configuration.usersecrets.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\7.0.0\\microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\7.0.0\\microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\7.0.0\\microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\7.0.0\\microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\7.0.0\\microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\7.0.1\\microsoft.extensions.hosting.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\7.0.0\\microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\7.0.0\\microsoft.extensions.logging.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\7.0.0\\microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\7.0.0\\microsoft.extensions.logging.configuration.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\7.0.0\\microsoft.extensions.logging.console.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\7.0.0\\microsoft.extensions.logging.debug.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\7.0.0\\microsoft.extensions.logging.eventlog.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\7.0.0\\microsoft.extensions.logging.eventsource.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\7.0.1\\microsoft.extensions.options.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\7.0.0\\microsoft.extensions.options.configurationextensions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\7.0.0\\microsoft.extensions.primitives.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua\\1.4.371.86\\opcfoundation.netstandard.opc.ua.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.bindings.https\\1.4.371.86\\opcfoundation.netstandard.opc.ua.bindings.https.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.client\\1.4.371.86\\opcfoundation.netstandard.opc.ua.client.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.client.complextypes\\1.4.371.86\\opcfoundation.netstandard.opc.ua.client.complextypes.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.configuration\\1.4.371.86\\opcfoundation.netstandard.opc.ua.configuration.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.core\\1.4.371.86\\opcfoundation.netstandard.opc.ua.core.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.gds.client.common\\1.4.371.86\\opcfoundation.netstandard.opc.ua.gds.client.common.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.gds.server.common\\1.4.371.86\\opcfoundation.netstandard.opc.ua.gds.server.common.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.security.certificates\\1.4.371.86\\opcfoundation.netstandard.opc.ua.security.certificates.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opcfoundation.netstandard.opc.ua.server\\1.4.371.86\\opcfoundation.netstandard.opc.ua.server.1.4.371.86.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\portable.bouncycastle\\1.9.0\\portable.bouncycastle.1.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\7.0.1\\system.diagnostics.diagnosticsource.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\7.0.0\\system.diagnostics.eventlog.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\6.0.0\\system.formats.asn1.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.2\\system.security.cryptography.x509certificates.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\7.0.0\\system.text.encodings.web.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\7.0.0\\system.text.json.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\6.0.36\\microsoft.netcore.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\6.0.36\\microsoft.windowsdesktop.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\6.0.36\\microsoft.aspnetcore.app.ref.6.0.36.nupkg.sha512"], "logs": [{"code": "NU1902", "level": "Warning", "message": "Package 'OPCFoundation.NetStandard.Opc.Ua' 1.4.371.86 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-7vfh-cqpc-4267", "projectPath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "warningLevel": 1, "filePath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "libraryId": "OPCFoundation.NetStandard.Opc.Ua", "targetGraphs": ["net6.0"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'OPCFoundation.NetStandard.Opc.Ua' 1.4.371.86 has a known high severity vulnerability, https://github.com/advisories/GHSA-qm9f-c3v9-wphv", "projectPath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "warningLevel": 1, "filePath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "libraryId": "OPCFoundation.NetStandard.Opc.Ua", "targetGraphs": ["net6.0"]}]}