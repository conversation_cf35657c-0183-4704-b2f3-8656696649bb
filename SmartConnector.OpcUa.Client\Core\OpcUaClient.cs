using Microsoft.Extensions.Logging;
using Opc.Ua;
using Opc.Ua.Client;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// Simple, working OPC UA client implementation
    /// </summary>
    public class OpcUaClient : IOpcUaClient
    {
        private readonly ILogger<OpcUaClient> _logger;
        private readonly OpcUaClientConfiguration _config;
        private Session? _session;
        private ApplicationConfiguration? _applicationConfiguration;
        private bool _disposed = false;

        public bool IsConnected => _session?.Connected == true;
        public Session? Session => _session;

        public event EventHandler<OpcUaConnectionEventArgs>? ConnectionStatusChanged;
        public event EventHandler<OpcUaNotificationEventArgs>? DataChanged;
        public event EventHandler<OpcUaEventNotificationArgs>? EventReceived;
        public event EventHandler<OpcUaErrorEventArgs>? ErrorOccurred;

        public OpcUaClient(OpcUaClientConfiguration config, ILogger<OpcUaClient> logger)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<OpcUaOperationResult> ConnectAsync()
        {
            try
            {
                if (IsConnected)
                {
                    return new OpcUaOperationResult { IsSuccess = true, Message = "Already connected" };
                }

                // Create application configuration
                _applicationConfiguration = new ApplicationConfiguration
                {
                    ApplicationName = _config.ApplicationName,
                    ApplicationUri = _config.ApplicationUri ?? $"urn:{Environment.MachineName}:{_config.ApplicationName}",
                    ApplicationType = ApplicationType.Client,
                    SecurityConfiguration = new SecurityConfiguration
                    {
                        ApplicationCertificate = new CertificateIdentifier(),
                        TrustedIssuerCertificates = new CertificateTrustList(),
                        TrustedPeerCertificates = new CertificateTrustList(),
                        RejectedCertificateStore = new CertificateStoreIdentifier(),
                        AutoAcceptUntrustedCertificates = true
                    },
                    TransportConfigurations = new TransportConfigurationCollection(),
                    TransportQuotas = new TransportQuotas { OperationTimeout = 15000 },
                    ClientConfiguration = new ClientConfiguration { DefaultSessionTimeout = 60000 }
                };

                await _applicationConfiguration.Validate(ApplicationType.Client);

                // Select endpoint
                var endpointUrl = _config.EndpointUrl;
                var endpointConfiguration = EndpointConfiguration.Create(_applicationConfiguration);
                var endpoint = CoreClientUtils.SelectEndpoint(endpointUrl, false);
                var configuredEndpoint = new ConfiguredEndpoint(null, endpoint, endpointConfiguration);

                // Create session
                _session = await Session.Create(
                    _applicationConfiguration,
                    configuredEndpoint,
                    false,
                    _config.ApplicationName,
                    60000,
                    new UserIdentity(new AnonymousIdentityToken()),
                    null);

                _logger.LogInformation("Connected to OPC UA server: {EndpointUrl}", endpointUrl);
                
                ConnectionStatusChanged?.Invoke(this, new OpcUaConnectionEventArgs { IsConnected = true });

                return new OpcUaOperationResult { IsSuccess = true, Message = "Connected successfully" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to OPC UA server");
                return new OpcUaOperationResult 
                { 
                    IsSuccess = false, 
                    Message = "Connection failed", 
                    ErrorDetails = ex.Message,
                    Exception = ex 
                };
            }
        }

        public async Task<OpcUaOperationResult> DisconnectAsync()
        {
            try
            {
                if (_session != null)
                {
                    _session.Close();
                    _session.Dispose();
                    _session = null;
                }

                _logger.LogInformation("Disconnected from OPC UA server");
                ConnectionStatusChanged?.Invoke(this, new OpcUaConnectionEventArgs { IsConnected = false });

                return new OpcUaOperationResult { IsSuccess = true, Message = "Disconnected successfully" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during disconnect");
                return new OpcUaOperationResult 
                { 
                    IsSuccess = false, 
                    Message = "Disconnect failed", 
                    ErrorDetails = ex.Message,
                    Exception = ex 
                };
            }
        }

        public async Task<OpcUaOperationResult> ReconnectAsync()
        {
            await DisconnectAsync();
            await Task.Delay(1000);
            return await ConnectAsync();
        }

        public async Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseAsync(NodeId? startingNode = null, int maxReferencesToReturn = 1000)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<List<OpcUaNode>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var nodeToBrowse = startingNode ?? ObjectIds.ObjectsFolder;
                var browseDescription = new BrowseDescription
                {
                    NodeId = nodeToBrowse,
                    BrowseDirection = BrowseDirection.Forward,
                    ReferenceTypeId = ReferenceTypeIds.References,
                    IncludeSubtypes = true,
                    NodeClassMask = 0,
                    ResultMask = (uint)BrowseResultMask.All
                };

                BrowseResultCollection results;
                DiagnosticInfoCollection diagnostics;

                var response = _session.Browse(null, null, (uint)maxReferencesToReturn,
                    new BrowseDescriptionCollection { browseDescription }, out results, out diagnostics);

                var nodes = new List<OpcUaNode>();
                if (results?.Count > 0)
                {
                    foreach (var reference in results[0].References)
                    {
                        nodes.Add(new OpcUaNode
                        {
                            NodeId = ExpandedNodeId.ToNodeId(reference.NodeId, _session.NamespaceUris),
                            DisplayName = reference.DisplayName?.Text ?? string.Empty,
                            BrowseName = reference.BrowseName?.Name ?? string.Empty,
                            NodeClass = reference.NodeClass
                        });
                    }
                }

                return new OpcUaOperationResult<List<OpcUaNode>>
                {
                    IsSuccess = true,
                    Message = $"Browsed {nodes.Count} nodes",
                    Data = nodes
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error browsing nodes");
                return new OpcUaOperationResult<List<OpcUaNode>>
                {
                    IsSuccess = false,
                    Message = "Browse failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        public async Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseRecursiveAsync(NodeId? startingNode = null, int maxDepth = 5)
        {
            var allNodes = new List<OpcUaNode>();
            var result = await BrowseAsync(startingNode);
            
            if (result.IsSuccess && result.Data != null)
            {
                allNodes.AddRange(result.Data);
            }

            return new OpcUaOperationResult<List<OpcUaNode>>
            {
                IsSuccess = result.IsSuccess,
                Message = result.Message,
                Data = allNodes
            };
        }

        public async Task<OpcUaOperationResult<List<OpcUaValue>>> ReadAsync(List<NodeId> nodeIds)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<List<OpcUaValue>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var nodesToRead = nodeIds.Select(nodeId => new ReadValueId
                {
                    NodeId = nodeId,
                    AttributeId = Attributes.Value
                }).ToList();

                DataValueCollection results;
                DiagnosticInfoCollection diagnostics;

                var response = _session.Read(null, 0, TimestampsToReturn.Both,
                    new ReadValueIdCollection(nodesToRead), out results, out diagnostics);

                var values = new List<OpcUaValue>();
                if (results != null)
                {
                    for (int i = 0; i < results.Count && i < nodeIds.Count; i++)
                    {
                        var result = results[i];
                        values.Add(new OpcUaValue
                        {
                            NodeId = nodeIds[i],
                            Value = result.Value,
                            StatusCode = result.StatusCode,
                            SourceTimestamp = result.SourceTimestamp,
                            ServerTimestamp = result.ServerTimestamp
                        });
                    }
                }

                return new OpcUaOperationResult<List<OpcUaValue>>
                {
                    IsSuccess = true,
                    Message = $"Read {values.Count} values",
                    Data = values
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading node values");
                return new OpcUaOperationResult<List<OpcUaValue>>
                {
                    IsSuccess = false,
                    Message = "Read failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        public async Task<OpcUaOperationResult<OpcUaValue>> ReadAsync(NodeId nodeId)
        {
            var result = await ReadAsync(new List<NodeId> { nodeId });
            
            if (result.IsSuccess && result.Data?.Count > 0)
            {
                return new OpcUaOperationResult<OpcUaValue>
                {
                    IsSuccess = true,
                    Message = result.Message,
                    Data = result.Data[0]
                };
            }

            return new OpcUaOperationResult<OpcUaValue>
            {
                IsSuccess = false,
                Message = result.Message,
                ErrorDetails = result.ErrorDetails,
                Exception = result.Exception
            };
        }

        // Simplified implementations for remaining interface methods
        public Task<OpcUaOperationResult> WriteAsync(List<OpcUaWriteValue> writeValues) => 
            Task.FromResult(new OpcUaOperationResult { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult> WriteAsync(NodeId nodeId, object value) => 
            Task.FromResult(new OpcUaOperationResult { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<object[]>> CallMethodAsync(NodeId objectId, NodeId methodId, params object[] inputArguments) => 
            Task.FromResult(new OpcUaOperationResult<object[]> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<uint>> CreateSubscriptionAsync(OpcUaSubscriptionSettings settings) => 
            Task.FromResult(new OpcUaOperationResult<uint> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult> AddMonitoredItemsAsync(uint subscriptionId, List<OpcUaMonitoredItem> monitoredItems) => 
            Task.FromResult(new OpcUaOperationResult { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult> RemoveMonitoredItemsAsync(uint subscriptionId, List<uint> monitoredItemIds) => 
            Task.FromResult(new OpcUaOperationResult { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult> DeleteSubscriptionAsync(uint subscriptionId) => 
            Task.FromResult(new OpcUaOperationResult { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<List<EndpointDescription>>> GetEndpointsAsync() => 
            Task.FromResult(new OpcUaOperationResult<List<EndpointDescription>> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<OpcUaServerStatus>> GetServerStatusAsync() => 
            Task.FromResult(new OpcUaOperationResult<OpcUaServerStatus> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<OpcUaServerInfo>> GetServerInfoAsync() => 
            Task.FromResult(new OpcUaOperationResult<OpcUaServerInfo> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<OpcUaNodeAttributes>> ReadNodeAttributesAsync(NodeId nodeId) => 
            Task.FromResult(new OpcUaOperationResult<OpcUaNodeAttributes> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<List<OpcUaHistoryValue>>> ReadHistoryAsync(NodeId nodeId, DateTime startTime, DateTime endTime, uint maxValues = 1000) => 
            Task.FromResult(new OpcUaOperationResult<List<OpcUaHistoryValue>> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<uint>> SubscribeToEventsAsync(NodeId? sourceNode = null, List<NodeId>? eventTypes = null) => 
            Task.FromResult(new OpcUaOperationResult<uint> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult> RegisterCustomDataTypeAsync(Type customType) => 
            Task.FromResult(new OpcUaOperationResult { IsSuccess = false, Message = "Not implemented in simple version" });

        public NamespaceTable GetNamespaceTable() => _session?.NamespaceUris ?? new NamespaceTable();

        public Task<OpcUaOperationResult<List<NodeId>>> TranslateBrowsePathsAsync(List<BrowsePath> browsePaths) => 
            Task.FromResult(new OpcUaOperationResult<List<NodeId>> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<List<ApplicationDescription>>> FindServersAsync(string? discoveryUrl = null) => 
            Task.FromResult(new OpcUaOperationResult<List<ApplicationDescription>> { IsSuccess = false, Message = "Not implemented in simple version" });

        public Task<OpcUaOperationResult<List<ServerOnNetwork>>> FindServersOnNetworkAsync(string? discoveryUrl = null) => 
            Task.FromResult(new OpcUaOperationResult<List<ServerOnNetwork>> { IsSuccess = false, Message = "Not implemented in simple version" });

        public void Dispose()
        {
            if (!_disposed)
            {
                _session?.Dispose();
                _disposed = true;
            }
        }
    }
}
