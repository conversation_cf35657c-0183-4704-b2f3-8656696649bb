const express = require('express');
const router = express.Router();
const EWSClient = require('../clients/EWSClient');
const config = require('../config/config');
const logger = require('../utils/logger');

// Initialize EWS client
const ewsClient = new EWSClient(config.ews);

/**
 * Test EWS connection
 */
router.get('/test', async (req, res) => {
  try {
    const result = await ewsClient.testConnection();
    res.json(result);
  } catch (error) {
    logger.error('EWS connection test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Connection test failed',
      error: error.message
    });
  }
});

/**
 * Get EWS connection status
 */
router.get('/status', (req, res) => {
  try {
    const status = ewsClient.getConnectionStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to get EWS status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get EWS status',
      error: error.message
    });
  }
});

/**
 * Send email
 */
router.post('/email/send', async (req, res) => {
  try {
    const { to, cc, bcc, subject, body, isHtml, attachments } = req.body;

    // Validation
    if (!to || !subject || !body) {
      return res.status(400).json({
        success: false,
        message: 'to, subject, and body are required fields'
      });
    }

    const emailData = {
      to,
      cc,
      bcc,
      subject,
      body,
      isHtml: isHtml || false,
      attachments: attachments || []
    };

    const result = await ewsClient.sendEmail(emailData);
    res.json(result);
  } catch (error) {
    logger.error('Failed to send email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send email',
      error: error.message
    });
  }
});

/**
 * Get inbox messages
 */
router.get('/email/inbox', async (req, res) => {
  try {
    const maxItems = parseInt(req.query.maxItems) || 10;
    const messages = await ewsClient.getInboxMessages(maxItems);
    
    res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    logger.error('Failed to get inbox messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get inbox messages',
      error: error.message
    });
  }
});

/**
 * Create calendar appointment
 */
router.post('/calendar/appointment', async (req, res) => {
  try {
    const { subject, body, start, end, location, attendees, isAllDay } = req.body;

    // Validation
    if (!subject || !start || !end) {
      return res.status(400).json({
        success: false,
        message: 'subject, start, and end are required fields'
      });
    }

    const startDate = new Date(start);
    const endDate = new Date(end);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format for start or end'
      });
    }

    if (startDate >= endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date must be before end date'
      });
    }

    const appointmentData = {
      subject,
      body,
      start: startDate,
      end: endDate,
      location,
      attendees: attendees || [],
      isAllDay: isAllDay || false
    };

    const result = await ewsClient.createAppointment(appointmentData);
    res.json(result);
  } catch (error) {
    logger.error('Failed to create appointment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create appointment',
      error: error.message
    });
  }
});

/**
 * Get calendar events
 */
router.get('/calendar/events', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'startDate and endDate query parameters are required'
      });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format for startDate or endDate'
      });
    }

    const events = await ewsClient.getCalendarEvents(start, end);
    res.json({
      success: true,
      data: events
    });
  } catch (error) {
    logger.error('Failed to get calendar events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get calendar events',
      error: error.message
    });
  }
});

/**
 * Create task
 */
router.post('/task', async (req, res) => {
  try {
    const { subject, body, dueDate, priority, status } = req.body;

    // Validation
    if (!subject) {
      return res.status(400).json({
        success: false,
        message: 'subject is required'
      });
    }

    const taskData = {
      subject,
      body,
      priority: priority || 'Normal',
      status: status || 'NotStarted'
    };

    if (dueDate) {
      const due = new Date(dueDate);
      if (isNaN(due.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format for dueDate'
        });
      }
      taskData.dueDate = due;
    }

    const result = await ewsClient.createTask(taskData);
    res.json(result);
  } catch (error) {
    logger.error('Failed to create task:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create task',
      error: error.message
    });
  }
});

module.exports = router;
