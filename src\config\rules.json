{"rules": {"alarm-notification": {"type": "tag-change", "tagId": "Channel1.Device1.AlarmStatus", "description": "Send email notification when alarm status goes high", "enabled": true, "condition": "return currentValue === true && previousValue === false;", "action": {"type": "email", "to": ["<EMAIL>"], "subject": "ALARM: Equipment Alert", "bodyTemplate": "Alarm triggered at {{timestamp}}\nTag: {{tagId}}\nValue: {{value}}"}}, "maintenance-schedule": {"type": "tag-change", "tagId": "Channel1.Device1.MaintenanceRequired", "description": "Create calendar appointment when maintenance is required", "enabled": true, "condition": "return currentValue === true && previousValue === false;", "action": {"type": "calendar", "subject": "Equipment Maintenance Required", "bodyTemplate": "Maintenance required for equipment.\nTag: {{tagId}}\nTriggered: {{timestamp}}", "location": "Production Floor", "attendees": ["<EMAIL>", "<EMAIL>"], "duration": 60, "offsetHours": 24}}, "daily-report": {"type": "scheduled", "schedule": "0 17 * * 1-5", "description": "Send daily production report at 5 PM on weekdays", "enabled": true, "action": {"type": "report", "reportType": "production", "tags": ["Channel1.Device1.ProductionCount", "Channel1.Device1.EfficiencyPercent", "Channel1.Device1.DowntimeMinutes"], "to": ["<EMAIL>", "<EMAIL>"], "subject": "Daily Production Report - {{date}}"}}, "temperature-warning": {"type": "tag-change", "tagId": "Channel1.Device1.Temperature", "description": "Send warning when temperature exceeds threshold", "enabled": true, "condition": "return currentValue > 80 && (previousValue <= 80 || previousValue === undefined);", "action": {"type": "email", "to": ["<EMAIL>", "<EMAIL>"], "subject": "WARNING: High Temperature Alert", "bodyTemplate": "Temperature warning at {{timestamp}}\nTag: {{tagId}}\nCurrent Temperature: {{value}}°C\nThreshold: 80°C"}}, "production-milestone": {"type": "tag-change", "tagId": "Channel1.Device1.ProductionCount", "description": "Celebrate production milestones", "enabled": true, "condition": "return currentValue > 0 && currentValue % 1000 === 0;", "action": {"type": "email", "to": ["<EMAIL>"], "subject": "🎉 Production Milestone Reached!", "bodyTemplate": "Congratulations! We've reached {{value}} units produced!\nTimestamp: {{timestamp}}", "isHtml": true}}}, "templates": {"email": {"alarm": {"subject": "ALARM: {{alarmType}}", "body": "An alarm has been triggered:\n\nTag: {{tagId}}\nValue: {{value}}\nTimestamp: {{timestamp}}\nDescription: {{description}}"}, "warning": {"subject": "WARNING: {{warningType}}", "body": "A warning condition has been detected:\n\nTag: {{tagId}}\nValue: {{value}}\nThreshold: {{threshold}}\nTimestamp: {{timestamp}}"}, "report": {"subject": "{{reportType}} Report - {{date}}", "body": "Please find the {{reportType}} report below:\n\n{{reportContent}}\n\nGenerated: {{timestamp}}"}}, "calendar": {"maintenance": {"subject": "Scheduled Maintenance - {{equipment}}", "body": "Maintenance scheduled for {{equipment}}\n\nDetails:\n{{details}}\n\nPlease ensure all safety protocols are followed."}, "meeting": {"subject": "{{meetingType}} - {{topic}}", "body": "Meeting scheduled to discuss {{topic}}\n\nAgenda:\n{{agenda}}"}}}, "settings": {"defaultEmailSettings": {"from": "<EMAIL>", "replyTo": "<EMAIL>", "priority": "Normal"}, "defaultCalendarSettings": {"defaultDuration": 60, "defaultLocation": "Conference Room A", "reminderMinutes": 15}, "retrySettings": {"maxRetries": 3, "retryDelay": 5000, "exponentialBackoff": true}}}