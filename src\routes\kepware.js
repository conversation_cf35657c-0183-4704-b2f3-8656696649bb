const express = require('express');
const router = express.Router();
const KepwareClient = require('../clients/KepwareClient');
const config = require('../config/config');
const logger = require('../utils/logger');

// Initialize Kepware client
const kepwareClient = new KepwareClient(config.kepware);

/**
 * Test Kepware connection
 */
router.get('/test', async (req, res) => {
  try {
    const result = await kepwareClient.testConnection();
    res.json(result);
  } catch (error) {
    logger.error('Kepware connection test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Connection test failed',
      error: error.message
    });
  }
});

/**
 * Get server status
 */
router.get('/status', async (req, res) => {
  try {
    const status = await kepwareClient.getServerStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to get Kepware server status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get server status',
      error: error.message
    });
  }
});

/**
 * Get project information
 */
router.get('/project', async (req, res) => {
  try {
    const projectInfo = await kepwareClient.getProjectInfo();
    res.json({
      success: true,
      data: projectInfo
    });
  } catch (error) {
    logger.error('Failed to get project info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get project information',
      error: error.message
    });
  }
});

/**
 * Get all channels
 */
router.get('/channels', async (req, res) => {
  try {
    const channels = await kepwareClient.getChannels();
    res.json({
      success: true,
      data: channels
    });
  } catch (error) {
    logger.error('Failed to get channels:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get channels',
      error: error.message
    });
  }
});

/**
 * Get devices for a channel
 */
router.get('/channels/:channelName/devices', async (req, res) => {
  try {
    const { channelName } = req.params;
    const devices = await kepwareClient.getDevices(channelName);
    res.json({
      success: true,
      data: devices
    });
  } catch (error) {
    logger.error('Failed to get devices:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get devices',
      error: error.message
    });
  }
});

/**
 * Get tags for a device
 */
router.get('/channels/:channelName/devices/:deviceName/tags', async (req, res) => {
  try {
    const { channelName, deviceName } = req.params;
    const tags = await kepwareClient.getTags(channelName, deviceName);
    res.json({
      success: true,
      data: tags
    });
  } catch (error) {
    logger.error('Failed to get tags:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tags',
      error: error.message
    });
  }
});

/**
 * Read tag values
 */
router.post('/read', async (req, res) => {
  try {
    const { tagIds } = req.body;
    
    if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'tagIds array is required'
      });
    }

    const result = await kepwareClient.readTags(tagIds);
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Failed to read tags:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to read tags',
      error: error.message
    });
  }
});

/**
 * Write tag values
 */
router.post('/write', async (req, res) => {
  try {
    const { writeOperations } = req.body;
    
    if (!writeOperations || !Array.isArray(writeOperations) || writeOperations.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'writeOperations array is required'
      });
    }

    const result = await kepwareClient.writeTags(writeOperations);
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Failed to write tags:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to write tags',
      error: error.message
    });
  }
});

/**
 * Get tag history
 */
router.get('/history/:tagId', async (req, res) => {
  try {
    const { tagId } = req.params;
    const { startTime, endTime } = req.query;
    
    if (!startTime || !endTime) {
      return res.status(400).json({
        success: false,
        message: 'startTime and endTime query parameters are required'
      });
    }

    const start = new Date(startTime);
    const end = new Date(endTime);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format for startTime or endTime'
      });
    }

    const history = await kepwareClient.getTagHistory(tagId, start, end);
    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    logger.error('Failed to get tag history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tag history',
      error: error.message
    });
  }
});

module.exports = router;
