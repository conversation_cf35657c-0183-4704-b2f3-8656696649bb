
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for routes</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> routes</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.1% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>78/268</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>18/100</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">21.42% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/28</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.21% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>78/267</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="connector.js"><a href="connector.js.html">connector.js</a></td>
	<td data-value="14.41" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 14%"></div><div class="cover-empty" style="width: 86%"></div></div>
	</td>
	<td data-value="14.41" class="pct low">14.41%</td>
	<td data-value="111" class="abs low">16/111</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="38" class="abs low">0/38</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="14.54" class="pct low">14.54%</td>
	<td data-value="110" class="abs low">16/110</td>
	</tr>

<tr>
	<td class="file low" data-value="ews.js"><a href="ews.js.html">ews.js</a></td>
	<td data-value="17.5" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.5" class="pct low">17.5%</td>
	<td data-value="80" class="abs low">14/80</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="44" class="abs low">0/44</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="17.5" class="pct low">17.5%</td>
	<td data-value="80" class="abs low">14/80</td>
	</tr>

<tr>
	<td class="file medium" data-value="kepware.js"><a href="kepware.js.html">kepware.js</a></td>
	<td data-value="62.33" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 62%"></div><div class="cover-empty" style="width: 38%"></div></div>
	</td>
	<td data-value="62.33" class="pct medium">62.33%</td>
	<td data-value="77" class="abs medium">48/77</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="18" class="abs high">18/18</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="9" class="abs medium">6/9</td>
	<td data-value="62.33" class="pct medium">62.33%</td>
	<td data-value="77" class="abs medium">48/77</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-12T15:03:42.400Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    