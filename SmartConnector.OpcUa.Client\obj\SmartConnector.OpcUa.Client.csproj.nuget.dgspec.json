{"format": 1, "restore": {"G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj": {}}, "projects": {"G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "projectName": "SmartConnector.OpcUa.Client", "projectPath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\SmartConnector.OpcUa.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\ua\\client\\SmartConnector.OpcUa.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[7.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OPCFoundation.NetStandard.Opc.Ua": {"target": "Package", "version": "[1.4.368.58, )"}, "OPCFoundation.NetStandard.Opc.Ua.Client": {"target": "Package", "version": "[1.4.368.58, )"}, "System.Security.Cryptography.X509Certificates": {"target": "Package", "version": "[4.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}