using Microsoft.Exchange.WebServices.Data;
using Microsoft.Extensions.Logging;
using System.Net;

namespace SmartConnector.Ews.Server
{
    /// <summary>
    /// Main EWS Server implementation for SmartConnector
    /// </summary>
    public class EwsServer : IEwsServer, IDisposable
    {
        private readonly ILogger<EwsServer> _logger;
        private readonly EwsConfiguration _config;
        private ExchangeService? _exchangeService;
        private bool _isDisposed = false;

        public EwsServer(EwsConfiguration config, ILogger<EwsServer> logger)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeExchangeService();
        }

        /// <summary>
        /// Initialize Exchange Service with configuration
        /// </summary>
        private void InitializeExchangeService()
        {
            try
            {
                _exchangeService = new ExchangeService(_config.ExchangeVersion);
                
                // Set credentials
                if (_config.UseDefaultCredentials)
                {
                    _exchangeService.UseDefaultCredentials = true;
                }
                else
                {
                    if (!string.IsNullOrEmpty(_config.Domain))
                    {
                        _exchangeService.Credentials = new NetworkCredential(_config.Username, _config.Password, _config.Domain);
                    }
                    else
                    {
                        _exchangeService.Credentials = new NetworkCredential(_config.Username, _config.Password);
                    }
                }

                // Set URL if provided
                if (!string.IsNullOrEmpty(_config.ExchangeUrl))
                {
                    _exchangeService.Url = new Uri(_config.ExchangeUrl);
                }

                // Configure tracing
                if (_config.EnableTracing && !string.IsNullOrEmpty(_config.TraceFilePath))
                {
                    _exchangeService.TraceEnabled = true;
                    _exchangeService.TraceFlags = TraceFlags.All;
                    _exchangeService.TraceListener = new EwsTraceListener(_config.TraceFilePath);
                }

                // Set timeout
                _exchangeService.Timeout = TimeSpan.FromMinutes(_config.TimeoutMinutes);

                // Handle certificate validation
                if (_config.AcceptInvalidCertificates)
                {
                    ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
                }

                _logger.LogInformation("Exchange Service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Exchange Service");
                throw;
            }
        }

        /// <summary>
        /// Test connection to Exchange server
        /// </summary>
        public async Task<EwsConnectionResult> TestConnectionAsync()
        {
            try
            {
                if (_exchangeService == null)
                {
                    return new EwsConnectionResult
                    {
                        IsSuccess = false,
                        Message = "Exchange service not initialized"
                    };
                }

                // Try to autodiscover if URL not set
                if (_exchangeService.Url == null)
                {
                    await _exchangeService.AutodiscoverUrl(_config.Username);
                }

                // Test by getting inbox folder
                var inbox = await Folder.Bind(_exchangeService, WellKnownFolderName.Inbox);
                
                var serverInfo = new EwsServerInfo
                {
                    ServerVersion = _exchangeService.ServerInfo?.MajorVersion.ToString() ?? "Unknown",
                    ExchangeVersion = _config.ExchangeVersion.ToString(),
                    TimeZone = _exchangeService.TimeZone?.DisplayName ?? "Unknown",
                    IsConnected = true,
                    LastConnectionTime = DateTime.UtcNow
                };

                _logger.LogInformation("EWS connection test successful");
                
                return new EwsConnectionResult
                {
                    IsSuccess = true,
                    Message = "Connection successful",
                    ServerInfo = serverInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EWS connection test failed");
                
                return new EwsConnectionResult
                {
                    IsSuccess = false,
                    Message = "Connection failed",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Send email message
        /// </summary>
        public async Task<EwsOperationResult> SendEmailAsync(EwsEmailMessage message)
        {
            try
            {
                if (_exchangeService == null)
                {
                    throw new InvalidOperationException("Exchange service not initialized");
                }

                var emailMessage = new EmailMessage(_exchangeService);
                
                // Set recipients
                foreach (var to in message.To)
                {
                    emailMessage.ToRecipients.Add(to);
                }
                
                foreach (var cc in message.Cc)
                {
                    emailMessage.CcRecipients.Add(cc);
                }
                
                foreach (var bcc in message.Bcc)
                {
                    emailMessage.BccRecipients.Add(bcc);
                }

                // Set message properties
                emailMessage.Subject = message.Subject;
                emailMessage.Body = new MessageBody(message.IsHtml ? BodyType.HTML : BodyType.Text, message.Body);
                emailMessage.Importance = ConvertImportance(message.Priority);

                // Add attachments
                foreach (var attachment in message.Attachments)
                {
                    if (attachment.Content != null)
                    {
                        emailMessage.Attachments.AddFileAttachment(attachment.Name, attachment.Content);
                    }
                }

                // Send the message
                await emailMessage.SendAndSaveCopy();

                _logger.LogInformation("Email sent successfully to {Recipients}", string.Join(", ", message.To));

                return new EwsOperationResult
                {
                    IsSuccess = true,
                    Message = "Email sent successfully",
                    OperationId = emailMessage.Id?.UniqueId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email");
                
                return new EwsOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to send email",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Create calendar appointment
        /// </summary>
        public async Task<EwsOperationResult> CreateAppointmentAsync(EwsAppointment appointment)
        {
            try
            {
                if (_exchangeService == null)
                {
                    throw new InvalidOperationException("Exchange service not initialized");
                }

                var appt = new Appointment(_exchangeService);
                
                // Set appointment properties
                appt.Subject = appointment.Subject;
                appt.Body = new MessageBody(BodyType.Text, appointment.Body);
                appt.Start = appointment.Start;
                appt.End = appointment.End;
                appt.Location = appointment.Location;
                appt.IsAllDayEvent = appointment.IsAllDay;
                appt.Importance = ConvertImportance(appointment.Priority);
                appt.LegacyFreeBusyStatus = ConvertFreeBusyStatus(appointment.FreeBusyStatus);

                // Add attendees
                foreach (var attendee in appointment.RequiredAttendees)
                {
                    appt.RequiredAttendees.Add(attendee);
                }
                
                foreach (var attendee in appointment.OptionalAttendees)
                {
                    appt.OptionalAttendees.Add(attendee);
                }

                // Set reminder
                if (appointment.ReminderMinutes > 0)
                {
                    appt.IsReminderSet = true;
                    appt.ReminderMinutesBeforeStart = appointment.ReminderMinutes;
                }

                // Save the appointment
                await appt.Save(SendInvitationsMode.SendToAllAndSaveCopy);

                _logger.LogInformation("Appointment created successfully: {Subject}", appointment.Subject);

                return new EwsOperationResult
                {
                    IsSuccess = true,
                    Message = "Appointment created successfully",
                    OperationId = appt.Id?.UniqueId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create appointment");
                
                return new EwsOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to create appointment",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Create task
        /// </summary>
        public async Task<EwsOperationResult> CreateTaskAsync(EwsTask task)
        {
            try
            {
                if (_exchangeService == null)
                {
                    throw new InvalidOperationException("Exchange service not initialized");
                }

                var ewsTask = new Task(_exchangeService);
                
                // Set task properties
                ewsTask.Subject = task.Subject;
                ewsTask.Body = new MessageBody(BodyType.Text, task.Body);
                ewsTask.Importance = ConvertImportance(task.Priority);
                
                if (task.DueDate.HasValue)
                {
                    ewsTask.DueDate = task.DueDate.Value;
                }
                
                if (task.StartDate.HasValue)
                {
                    ewsTask.StartDate = task.StartDate.Value;
                }

                ewsTask.PercentComplete = (int)task.PercentComplete;

                // Set reminder
                if (task.IsReminderSet && task.ReminderMinutes > 0)
                {
                    ewsTask.IsReminderSet = true;
                    ewsTask.ReminderMinutesBeforeStart = task.ReminderMinutes;
                }

                // Save the task
                await ewsTask.Save();

                _logger.LogInformation("Task created successfully: {Subject}", task.Subject);

                return new EwsOperationResult
                {
                    IsSuccess = true,
                    Message = "Task created successfully",
                    OperationId = ewsTask.Id?.UniqueId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create task");
                
                return new EwsOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to create task",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Convert EwsImportance to Exchange Importance
        /// </summary>
        private static Importance ConvertImportance(EwsImportance importance)
        {
            return importance switch
            {
                EwsImportance.Low => Importance.Low,
                EwsImportance.Normal => Importance.Normal,
                EwsImportance.High => Importance.High,
                _ => Importance.Normal
            };
        }

        /// <summary>
        /// Convert EwsFreeBusyStatus to LegacyFreeBusyStatus
        /// </summary>
        private static LegacyFreeBusyStatus ConvertFreeBusyStatus(EwsFreeBusyStatus status)
        {
            return status switch
            {
                EwsFreeBusyStatus.Free => LegacyFreeBusyStatus.Free,
                EwsFreeBusyStatus.Tentative => LegacyFreeBusyStatus.Tentative,
                EwsFreeBusyStatus.Busy => LegacyFreeBusyStatus.Busy,
                EwsFreeBusyStatus.OutOfOffice => LegacyFreeBusyStatus.OOF,
                EwsFreeBusyStatus.WorkingElsewhere => LegacyFreeBusyStatus.WorkingElsewhere,
                _ => LegacyFreeBusyStatus.Busy
            };
        }

        // Additional methods would be implemented here...
        public Task<EwsOperationResult<List<EwsEmailMessage>>> GetInboxMessagesAsync(int maxItems = 10)
        {
            throw new NotImplementedException();
        }

        public Task<EwsOperationResult<List<EwsAppointment>>> GetCalendarEventsAsync(DateTime startDate, DateTime endDate)
        {
            throw new NotImplementedException();
        }

        public Task<EwsOperationResult> SubscribeToMailboxChangesAsync(Action<EwsNotification> onNotification)
        {
            throw new NotImplementedException();
        }

        public Task<EwsOperationResult<EwsServerInfo>> GetServerInfoAsync()
        {
            throw new NotImplementedException();
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _exchangeService?.Dispose();
                _isDisposed = true;
            }
        }
    }
}
