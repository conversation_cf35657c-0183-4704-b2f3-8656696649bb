{"name": "ntlm-client", "version": "0.1.1", "description": "A node.js NTLM client with support for NTLM and NTLMv2 authentication", "keywords": ["ntlm", "ntlmv2", "client", "authentication"], "main": "./lib/index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/clncln1/node-ntlm-client.git"}, "bugs": {"url": "https://github.com/clncln1/node-ntlm-client/issues"}, "engines": {"node": ">=4.0.0"}, "license": "MIT", "dependencies": {"extend": "^3.0.0", "request": "^2.66.0"}}