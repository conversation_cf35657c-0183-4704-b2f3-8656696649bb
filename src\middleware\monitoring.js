const logger = require('../utils/logger');

/**
 * Performance monitoring middleware
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        averageResponseTime: 0
      },
      endpoints: new Map(),
      errors: new Map()
    };
    
    this.startTime = Date.now();
  }

  /**
   * Express middleware for request monitoring
   */
  middleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      const endpoint = `${req.method} ${req.route?.path || req.path}`;

      // Initialize endpoint metrics if not exists
      if (!this.metrics.endpoints.has(endpoint)) {
        this.metrics.endpoints.set(endpoint, {
          count: 0,
          totalTime: 0,
          errors: 0,
          averageTime: 0
        });
      }

      // Track response
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        const endpointMetrics = this.metrics.endpoints.get(endpoint);

        // Update global metrics
        this.metrics.requests.total++;
        if (res.statusCode < 400) {
          this.metrics.requests.success++;
        } else {
          this.metrics.requests.errors++;
        }

        // Update endpoint metrics
        endpointMetrics.count++;
        endpointMetrics.totalTime += duration;
        endpointMetrics.averageTime = endpointMetrics.totalTime / endpointMetrics.count;

        if (res.statusCode >= 400) {
          endpointMetrics.errors++;
        }

        // Update global average response time
        const totalRequests = this.metrics.requests.total;
        this.metrics.requests.averageResponseTime = 
          (this.metrics.requests.averageResponseTime * (totalRequests - 1) + duration) / totalRequests;

        // Log slow requests
        if (duration > 5000) {
          logger.warn('Slow request detected', {
            endpoint,
            duration,
            statusCode: res.statusCode,
            ip: req.ip
          });
        }

        // Log request details
        logger.debug('Request completed', {
          endpoint,
          duration,
          statusCode: res.statusCode,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
      });

      next();
    };
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    const uptime = Date.now() - this.startTime;
    const endpointMetrics = {};
    
    for (const [endpoint, metrics] of this.metrics.endpoints) {
      endpointMetrics[endpoint] = { ...metrics };
    }

    return {
      uptime,
      requests: { ...this.metrics.requests },
      endpoints: endpointMetrics,
      memory: process.memoryUsage(),
      cpu: process.cpuUsage()
    };
  }

  /**
   * Reset metrics
   */
  reset() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        averageResponseTime: 0
      },
      endpoints: new Map(),
      errors: new Map()
    };
    this.startTime = Date.now();
  }
}

/**
 * Health check middleware
 */
class HealthChecker {
  constructor() {
    this.checks = new Map();
  }

  /**
   * Register a health check
   */
  register(name, checkFn, options = {}) {
    this.checks.set(name, {
      fn: checkFn,
      timeout: options.timeout || 5000,
      critical: options.critical || false,
      interval: options.interval || 60000
    });
  }

  /**
   * Run all health checks
   */
  async runChecks() {
    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {}
    };

    for (const [name, check] of this.checks) {
      const startTime = Date.now();
      
      try {
        const result = await Promise.race([
          check.fn(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), check.timeout)
          )
        ]);

        results.checks[name] = {
          status: 'healthy',
          result,
          duration: Date.now() - startTime,
          critical: check.critical
        };
      } catch (error) {
        results.checks[name] = {
          status: 'unhealthy',
          error: error.message,
          duration: Date.now() - startTime,
          critical: check.critical
        };

        if (check.critical) {
          results.status = 'unhealthy';
        }
      }
    }

    return results;
  }

  /**
   * Express middleware for health endpoint
   */
  middleware() {
    return async (req, res) => {
      try {
        const health = await this.runChecks();
        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);
      } catch (error) {
        logger.error('Health check failed:', error);
        res.status(503).json({
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    };
  }
}

/**
 * Request correlation middleware
 */
function correlationMiddleware() {
  return (req, res, next) => {
    // Generate correlation ID if not present
    const correlationId = req.headers['x-correlation-id'] || 
                         req.headers['x-request-id'] || 
                         generateCorrelationId();

    // Add to request and response headers
    req.correlationId = correlationId;
    res.setHeader('X-Correlation-ID', correlationId);

    // Add to logger context
    req.logger = logger.child({ correlationId });

    next();
  };
}

/**
 * Generate correlation ID
 */
function generateCorrelationId() {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Security headers middleware
 */
function securityHeadersMiddleware() {
  return (req, res, next) => {
    // Security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'");
    
    next();
  };
}

/**
 * Request validation middleware
 */
function requestValidationMiddleware() {
  return (req, res, next) => {
    // Validate content type for POST/PUT requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.get('Content-Type');
      if (!contentType || !contentType.includes('application/json')) {
        return res.status(400).json({
          success: false,
          error: 'Content-Type must be application/json'
        });
      }
    }

    // Validate request size
    const contentLength = parseInt(req.get('Content-Length') || '0');
    if (contentLength > 10 * 1024 * 1024) { // 10MB limit
      return res.status(413).json({
        success: false,
        error: 'Request entity too large'
      });
    }

    next();
  };
}

module.exports = {
  PerformanceMonitor,
  HealthChecker,
  correlationMiddleware,
  securityHeadersMiddleware,
  requestValidationMiddleware
};
