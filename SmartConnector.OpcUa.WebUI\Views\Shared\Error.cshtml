@model SmartConnector.OpcUa.WebUI.Models.ErrorViewModel
@{
    ViewData["Title"] = "Error";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        An Error Occurred
                    </h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-bug fa-3x text-danger mb-3"></i>
                        <h5>Oops! Something went wrong.</h5>
                        <p class="text-muted">We're sorry, but an unexpected error has occurred while processing your request.</p>
                    </div>

                    @if (Model?.ShowRequestId == true)
                    {
                        <div class="alert alert-info">
                            <strong>Request ID:</strong> <code>@Model.RequestId</code>
                            <br>
                            <small class="text-muted">Please include this ID when reporting the issue.</small>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-lightbulb"></i> What you can do:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-arrow-right text-primary"></i> Try refreshing the page</li>
                                <li><i class="fas fa-arrow-right text-primary"></i> Go back to the previous page</li>
                                <li><i class="fas fa-arrow-right text-primary"></i> Return to the dashboard</li>
                                <li><i class="fas fa-arrow-right text-primary"></i> Check your connection to the OPC UA server</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-tools"></i> Common solutions:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Verify server connectivity</li>
                                <li><i class="fas fa-check text-success"></i> Check network settings</li>
                                <li><i class="fas fa-check text-success"></i> Restart the application</li>
                                <li><i class="fas fa-check text-success"></i> Contact system administrator</li>
                            </ul>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary">
                            <i class="fas fa-home"></i> Return to Dashboard
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Go Back
                        </button>
                        <button onclick="location.reload()" class="btn btn-outline-primary">
                            <i class="fas fa-sync"></i> Refresh Page
                        </button>
                    </div>
                </div>
                <div class="card-footer text-muted text-center">
                    <small>
                        <i class="fas fa-clock"></i>
                        Error occurred at: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-refresh after 30 seconds if user doesn't take action
    setTimeout(function() {
        if (confirm('Would you like to automatically return to the dashboard?')) {
            window.location.href = '@Url.Action("Index", "Home")';
        }
    }, 30000);

    // Log error for debugging
    console.error('Error page displayed', {
        requestId: '@Model?.RequestId',
        timestamp: '@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")',
        userAgent: navigator.userAgent,
        url: window.location.href
    });
</script>
