const SmartConnector = require('../src/services/SmartConnector');
const KepwareClient = require('../src/clients/KepwareClient');
const EWSClient = require('../src/clients/EWSClient');

// Mock the clients
jest.mock('../src/clients/KepwareClient');
jest.mock('../src/clients/EWSClient');

describe('SmartConnector', () => {
  let smartConnector;
  let mockKepwareClient;
  let mockEWSClient;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Create mock instances
    mockKepwareClient = {
      testConnection: jest.fn(),
      readTags: jest.fn(),
      writeTags: jest.fn()
    };
    
    mockEWSClient = {
      testConnection: jest.fn(),
      sendEmail: jest.fn(),
      createAppointment: jest.fn()
    };

    // Mock constructors
    KepwareClient.mockImplementation(() => mockKepwareClient);
    EWSClient.mockImplementation(() => mockEWSClient);

    smartConnector = new SmartConnector();
  });

  afterEach(async () => {
    if (smartConnector.isRunning) {
      await smartConnector.stop();
    }
  });

  describe('Initialization', () => {
    it('should initialize with default rules', () => {
      expect(smartConnector.rules.size).toBeGreaterThan(0);
      expect(smartConnector.rules.has('alarm-notification')).toBe(true);
      expect(smartConnector.rules.has('maintenance-schedule')).toBe(true);
      expect(smartConnector.rules.has('daily-report')).toBe(true);
    });

    it('should not be running initially', () => {
      expect(smartConnector.isRunning).toBe(false);
    });
  });

  describe('Start/Stop', () => {
    it('should start successfully with valid connections', async () => {
      mockKepwareClient.testConnection.mockResolvedValue({ success: true });
      mockEWSClient.testConnection.mockResolvedValue({ success: true });

      await smartConnector.start();

      expect(smartConnector.isRunning).toBe(true);
      expect(mockKepwareClient.testConnection).toHaveBeenCalled();
      expect(mockEWSClient.testConnection).toHaveBeenCalled();
    });

    it('should fail to start with invalid Kepware connection', async () => {
      mockKepwareClient.testConnection.mockResolvedValue({ 
        success: false, 
        message: 'Connection failed' 
      });
      mockEWSClient.testConnection.mockResolvedValue({ success: true });

      await expect(smartConnector.start()).rejects.toThrow('Kepware connection failed');
      expect(smartConnector.isRunning).toBe(false);
    });

    it('should fail to start with invalid EWS connection', async () => {
      mockKepwareClient.testConnection.mockResolvedValue({ success: true });
      mockEWSClient.testConnection.mockResolvedValue({ 
        success: false, 
        message: 'EWS connection failed' 
      });

      await expect(smartConnector.start()).rejects.toThrow('EWS connection failed');
      expect(smartConnector.isRunning).toBe(false);
    });

    it('should stop successfully', async () => {
      mockKepwareClient.testConnection.mockResolvedValue({ success: true });
      mockEWSClient.testConnection.mockResolvedValue({ success: true });

      await smartConnector.start();
      expect(smartConnector.isRunning).toBe(true);

      await smartConnector.stop();
      expect(smartConnector.isRunning).toBe(false);
    });
  });

  describe('Rule Management', () => {
    it('should add new rule', () => {
      const ruleId = 'test-rule';
      const rule = {
        type: 'tag-change',
        tagId: 'Test.Tag',
        condition: (current, previous) => current > previous,
        action: async () => {}
      };

      smartConnector.addRule(ruleId, rule);

      expect(smartConnector.rules.has(ruleId)).toBe(true);
      expect(smartConnector.rules.get(ruleId)).toEqual(rule);
    });

    it('should remove rule', () => {
      const ruleId = 'test-rule';
      const rule = {
        type: 'tag-change',
        tagId: 'Test.Tag',
        condition: (current, previous) => current > previous,
        action: async () => {}
      };

      smartConnector.addRule(ruleId, rule);
      expect(smartConnector.rules.has(ruleId)).toBe(true);

      smartConnector.removeRule(ruleId);
      expect(smartConnector.rules.has(ruleId)).toBe(false);
    });

    it('should add tag subscription for tag-change rules', () => {
      const ruleId = 'test-rule';
      const tagId = 'Test.Tag';
      const rule = {
        type: 'tag-change',
        tagId,
        condition: (current, previous) => current > previous,
        action: async () => {}
      };

      smartConnector.addRule(ruleId, rule);

      expect(smartConnector.tagSubscriptions.has(tagId)).toBe(true);
    });
  });

  describe('Tag Processing', () => {
    it('should process tag change and execute rule', async () => {
      const mockAction = jest.fn();
      const ruleId = 'test-rule';
      const tagId = 'Test.Tag';
      const rule = {
        type: 'tag-change',
        tagId,
        condition: (current, previous) => current > (previous || 0),
        action: mockAction
      };

      smartConnector.addRule(ruleId, rule);

      const tagResult = {
        id: tagId,
        v: 100,
        s: 'GOOD'
      };

      await smartConnector.processTagChange(tagResult);

      expect(mockAction).toHaveBeenCalledWith({
        tagId,
        value: 100,
        previousValue: undefined,
        timestamp: expect.any(Date)
      });
    });

    it('should not execute rule if condition not met', async () => {
      const mockAction = jest.fn();
      const ruleId = 'test-rule';
      const tagId = 'Test.Tag';
      const rule = {
        type: 'tag-change',
        tagId,
        condition: (current, previous) => current > 200,
        action: mockAction
      };

      smartConnector.addRule(ruleId, rule);

      const tagResult = {
        id: tagId,
        v: 100,
        s: 'GOOD'
      };

      await smartConnector.processTagChange(tagResult);

      expect(mockAction).not.toHaveBeenCalled();
    });

    it('should skip processing for bad quality tags', async () => {
      const mockAction = jest.fn();
      const ruleId = 'test-rule';
      const tagId = 'Test.Tag';
      const rule = {
        type: 'tag-change',
        tagId,
        condition: () => true,
        action: mockAction
      };

      smartConnector.addRule(ruleId, rule);

      const tagResult = {
        id: tagId,
        v: 100,
        s: 'BAD'
      };

      await smartConnector.processTagChange(tagResult);

      expect(mockAction).not.toHaveBeenCalled();
    });
  });

  describe('Status and Metrics', () => {
    it('should return correct status', () => {
      const status = smartConnector.getStatus();

      expect(status).toHaveProperty('isRunning');
      expect(status).toHaveProperty('rulesCount');
      expect(status).toHaveProperty('tagSubscriptions');
      expect(status).toHaveProperty('scheduledJobs');
      expect(status).toHaveProperty('lastPoll');

      expect(typeof status.isRunning).toBe('boolean');
      expect(typeof status.rulesCount).toBe('number');
      expect(typeof status.tagSubscriptions).toBe('number');
      expect(typeof status.scheduledJobs).toBe('number');
    });

    it('should return rules summary', () => {
      const rules = smartConnector.getRules();

      expect(typeof rules).toBe('object');
      expect(rules).toHaveProperty('alarm-notification');
      expect(rules).toHaveProperty('maintenance-schedule');
      expect(rules).toHaveProperty('daily-report');

      // Check rule structure
      const alarmRule = rules['alarm-notification'];
      expect(alarmRule).toHaveProperty('type');
      expect(alarmRule).toHaveProperty('tagId');
    });
  });

  describe('Production Report Generation', () => {
    it('should generate HTML production report', () => {
      const tagData = [
        { id: 'Channel1.Device1.ProductionCount', v: 1000, s: 'GOOD' },
        { id: 'Channel1.Device1.EfficiencyPercent', v: 85.5, s: 'GOOD' },
        { id: 'Channel1.Device1.DowntimeMinutes', v: 30, s: 'GOOD' }
      ];

      const report = smartConnector.generateProductionReport(tagData);

      expect(report).toContain('<table');
      expect(report).toContain('ProductionCount');
      expect(report).toContain('1000');
      expect(report).toContain('EfficiencyPercent');
      expect(report).toContain('85.5');
      expect(report).toContain('DowntimeMinutes');
      expect(report).toContain('30');
    });

    it('should handle empty tag data', () => {
      const report = smartConnector.generateProductionReport([]);

      expect(report).toContain('<table');
      expect(report).toContain('</table>');
    });
  });
});
