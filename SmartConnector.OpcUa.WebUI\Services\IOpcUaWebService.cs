using SmartConnector.OpcUa.WebUI.Models;

namespace SmartConnector.OpcUa.WebUI.Services
{
    public interface IOpcUaWebService
    {
        bool IsConnected { get; }
        string? ServerUrl { get; }
        
        Task<ServiceResult<bool>> ConnectAsync(string endpointUrl);
        Task<ServiceResult<bool>> DisconnectAsync();
        Task<ServiceResult<IEnumerable<OpcUaNodeInfo>>> BrowseAsync(string? nodeId = null);
        Task<ServiceResult<IEnumerable<OpcUaDataValue>>> ReadAsync(IEnumerable<string> nodeIds);
        Task<ServiceResult<bool>> WriteAsync(IEnumerable<MockWriteValue> writeValues);
        Task<uint> CreateSubscriptionAsync(IEnumerable<string> nodeIds, double publishingInterval);
        Task<ServiceResult<bool>> DeleteSubscriptionAsync(uint subscriptionId);
        Task<ServiceResult<IEnumerable<OpcUaServerInfo>>> FindServersAsync(string? discoveryUrl = null);
        Task StartAsync();
        Task StopAsync();
        
        event EventHandler<IEnumerable<OpcUaDataValue>>? DataChanged;
    }
}
