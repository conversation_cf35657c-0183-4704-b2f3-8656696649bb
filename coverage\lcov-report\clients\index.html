
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for clients</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> clients</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">16.55% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>24/145</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">2.12% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/47</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.42% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/35</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">16.55% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>24/145</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="EWSClient.js"><a href="EWSClient.js.html">EWSClient.js</a></td>
	<td data-value="26.92" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 26%"></div><div class="cover-empty" style="width: 74%"></div></div>
	</td>
	<td data-value="26.92" class="pct low">26.92%</td>
	<td data-value="78" class="abs low">21/78</td>
	<td data-value="2.56" class="pct low">2.56%</td>
	<td data-value="39" class="abs low">1/39</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="16" class="abs low">4/16</td>
	<td data-value="26.92" class="pct low">26.92%</td>
	<td data-value="78" class="abs low">21/78</td>
	</tr>

<tr>
	<td class="file low" data-value="KepwareClient.js"><a href="KepwareClient.js.html">KepwareClient.js</a></td>
	<td data-value="4.47" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.47" class="pct low">4.47%</td>
	<td data-value="67" class="abs low">3/67</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="4.47" class="pct low">4.47%</td>
	<td data-value="67" class="abs low">3/67</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-12T15:03:42.400Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    