namespace SmartConnector.OpcUa.WebUI.Models
{
    public class ServiceResult<T>
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public T? Data { get; set; }

        public static ServiceResult<T> Success(T data)
        {
            return new ServiceResult<T>
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static ServiceResult<T> Failure(string message)
        {
            return new ServiceResult<T>
            {
                IsSuccess = false,
                Message = message
            };
        }
    }

    public class OpcUaNodeInfo
    {
        public string NodeId { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string BrowseName { get; set; } = string.Empty;
        public string NodeClass { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool HasChildren { get; set; }
    }

    public class OpcUaDataValue
    {
        public string NodeId { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string StatusCode { get; set; } = "Good";
        public DateTime SourceTimestamp { get; set; } = DateTime.UtcNow;
        public DateTime ServerTimestamp { get; set; } = DateTime.UtcNow;
        public string? DisplayName { get; set; }
    }

    public class OpcUaServerInfo
    {
        public string ApplicationName { get; set; } = string.Empty;
        public string ApplicationUri { get; set; } = string.Empty;
        public string ApplicationType { get; set; } = string.Empty;
        public List<string> DiscoveryUrls { get; set; } = new();
    }

    public class OpcUaWriteValue
    {
        public string NodeId { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    public class MockWriteValue
    {
        public string NodeId { get; set; } = string.Empty;
        public object? Value { get; set; }
    }
}
