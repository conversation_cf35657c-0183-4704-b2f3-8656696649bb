# SmartConnector EWS Webserver

A powerful integration platform that bridges Kepware industrial data with Microsoft Exchange Web Services (EWS), enabling automated email notifications, calendar scheduling, and task management based on real-time industrial data.

## Features

- **Kepware Integration**: Real-time data reading/writing via Kepware REST API
- **Exchange Web Services**: Email, calendar, and task automation
- **Smart Rules Engine**: Configurable business logic for data-driven actions
- **Web API**: RESTful endpoints for configuration and monitoring
- **Real-time Monitoring**: Performance metrics and health checks
- **Robust Error Handling**: Comprehensive logging and error recovery
- **Template Engine**: Customizable email and calendar templates

## Quick Start

### Prerequisites

- Node.js 16.0 or higher
- Kepware server with REST API enabled
- Microsoft Exchange server or Office 365 account
- Network access between SmartConnector and both systems

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd smartconnector-ews-kepware
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the server:
```bash
npm start
```

For development with auto-reload:
```bash
npm run dev
```

## Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Kepware Configuration
KEPWARE_HOST=localhost
KEPWARE_PORT=57412
KEPWARE_USERNAME=Administrator
KEPWARE_PASSWORD=your-password
KEPWARE_USE_HTTPS=false

# EWS Configuration
EWS_URL=https://outlook.office365.com/EWS/Exchange.asmx
EWS_USERNAME=<EMAIL>
EWS_PASSWORD=your-password
EWS_DOMAIN=your-domain

# SmartConnector Configuration
CONNECTOR_POLL_INTERVAL=5000
API_KEY=your-secure-api-key
```

### Rules Configuration

Rules are defined in `src/config/rules.json` and can be managed via the API. Example rule:

```json
{
  "alarm-notification": {
    "type": "tag-change",
    "tagId": "Channel1.Device1.AlarmStatus",
    "condition": "return currentValue === true && previousValue === false;",
    "action": {
      "type": "email",
      "to": ["<EMAIL>"],
      "subject": "ALARM: Equipment Alert",
      "bodyTemplate": "Alarm triggered at {{timestamp}}\nTag: {{tagId}}\nValue: {{value}}"
    }
  }
}
```

## API Documentation

### Authentication

All API endpoints require an API key in the `X-API-Key` header:

```bash
curl -H "X-API-Key: your-api-key" http://localhost:3000/api/connector/status
```

### Core Endpoints

#### Health Check
```
GET /health
```
Returns server health status without authentication.

#### Connector Management
```
GET    /api/connector/status     # Get connector status
POST   /api/connector/start      # Start connector
POST   /api/connector/stop       # Stop connector
POST   /api/connector/restart    # Restart connector
```

#### Rules Management
```
GET    /api/connector/rules      # Get all rules
POST   /api/connector/rules      # Add new rule
DELETE /api/connector/rules/:id  # Remove rule
POST   /api/connector/rules/:id/test  # Test rule
```

#### Kepware Operations
```
GET    /api/kepware/test         # Test connection
GET    /api/kepware/status       # Get server status
GET    /api/kepware/channels     # Get channels
POST   /api/kepware/read         # Read tag values
POST   /api/kepware/write        # Write tag values
```

#### EWS Operations
```
GET    /api/ews/test             # Test connection
POST   /api/ews/email/send       # Send email
GET    /api/ews/email/inbox      # Get inbox messages
POST   /api/ews/calendar/appointment  # Create appointment
GET    /api/ews/calendar/events  # Get calendar events
POST   /api/ews/task             # Create task
```

### Example API Calls

#### Send Email
```bash
curl -X POST http://localhost:3000/api/ews/email/send \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "to": ["<EMAIL>"],
    "subject": "Test Email",
    "body": "This is a test email from SmartConnector",
    "isHtml": false
  }'
```

#### Read Kepware Tags
```bash
curl -X POST http://localhost:3000/api/kepware/read \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "tagIds": ["Channel1.Device1.Temperature", "Channel1.Device1.Pressure"]
  }'
```

#### Create Calendar Appointment
```bash
curl -X POST http://localhost:3000/api/ews/calendar/appointment \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Maintenance Meeting",
    "body": "Discuss equipment maintenance schedule",
    "start": "2024-01-15T09:00:00Z",
    "end": "2024-01-15T10:00:00Z",
    "location": "Conference Room A",
    "attendees": ["<EMAIL>"]
  }'
```

## Rule Types

### Tag Change Rules

Triggered when monitored tag values change:

```json
{
  "type": "tag-change",
  "tagId": "Channel1.Device1.Temperature",
  "condition": "return currentValue > 80;",
  "action": {
    "type": "email",
    "to": ["<EMAIL>"],
    "subject": "High Temperature Alert"
  }
}
```

### Scheduled Rules

Triggered on a cron schedule:

```json
{
  "type": "scheduled",
  "schedule": "0 17 * * 1-5",
  "action": {
    "type": "report",
    "reportType": "production",
    "tags": ["Channel1.Device1.ProductionCount"],
    "to": ["<EMAIL>"]
  }
}
```

## Template System

Use `{{variable}}` placeholders in templates:

- `{{tagId}}` - Tag identifier
- `{{value}}` - Current tag value
- `{{previousValue}}` - Previous tag value
- `{{timestamp}}` - Event timestamp
- `{{date}}` - Current date
- `{{time}}` - Current time

## Monitoring and Logging

### Log Files

Logs are written to `logs/smartconnector.log` by default. Configure in `.env`:

```env
LOG_LEVEL=info
LOG_FILE=logs/smartconnector.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
```

### Performance Metrics

Access performance metrics via:
```
GET /api/connector/status
```

### Health Checks

Monitor system health:
```
GET /health
```

## Development

### Running Tests

```bash
npm test
npm run test:watch
```

### Code Linting

```bash
npm run lint
npm run lint:fix
```

### Project Structure

```
src/
├── clients/          # External service clients
│   ├── KepwareClient.js
│   └── EWSClient.js
├── config/           # Configuration files
│   ├── config.js
│   └── rules.json
├── middleware/       # Express middleware
│   └── monitoring.js
├── routes/           # API route handlers
│   ├── connector.js
│   ├── kepware.js
│   └── ews.js
├── services/         # Business logic
│   └── SmartConnector.js
├── utils/            # Utility functions
│   ├── logger.js
│   ├── errorHandler.js
│   └── templateEngine.js
└── server.js         # Main server file
```

## Troubleshooting

### Common Issues

1. **Kepware Connection Failed**
   - Verify Kepware server is running
   - Check REST API is enabled
   - Validate credentials and network connectivity

2. **EWS Authentication Failed**
   - Verify Exchange server URL
   - Check username/password
   - For Office 365, ensure modern authentication is configured

3. **Rules Not Triggering**
   - Check tag IDs are correct
   - Verify rule conditions
   - Review logs for errors

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=debug
NODE_ENV=development
```

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions, please check the logs and refer to the troubleshooting section above.
