using Microsoft.AspNetCore.Mvc;
using SmartConnector.OpcUa.WebUI.Models;
using SmartConnector.OpcUa.WebUI.Services;
using System.Diagnostics;

namespace SmartConnector.OpcUa.WebUI.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly IOpcUaWebService _opcUaWebService;

        public HomeController(ILogger<HomeController> logger, IOpcUaWebService opcUaWebService)
        {
            _logger = logger;
            _opcUaWebService = opcUaWebService;
        }

        public async Task<IActionResult> Index()
        {
            var model = new DashboardViewModel
            {
                IsConnected = _opcUaWebService.IsConnected,
                ConnectionStatus = _opcUaWebService.IsConnected ? "Connected" : "Disconnected",
                ServerUrl = _opcUaWebService.ServerUrl,
                LastUpdate = DateTime.Now
            };

            if (_opcUaWebService.IsConnected)
            {
                var serverInfoResult = await _mockOpcUaWebService.GetServerInfoAsync();
                if (serverInfoResult.IsSuccess && serverInfoResult.Data != null)
                {
                    model.ServerInfo = new DashboardServerInfo
                    {
                        ApplicationName = serverInfoResult.Data.ApplicationName,
                        ApplicationUri = serverInfoResult.Data.ApplicationUri,
                        ProductUri = serverInfoResult.Data.ProductUri,
                        SoftwareVersion = serverInfoResult.Data.SoftwareVersion,
                        BuildNumber = serverInfoResult.Data.BuildNumber,
                        BuildDate = serverInfoResult.Data.BuildDate,
                        ServerCapabilities = serverInfoResult.Data.ServerCapabilities,
                        SupportedProfiles = serverInfoResult.Data.SupportedProfiles
                    };
                }

                var recentValues = await _mockOpcUaWebService.GetRecentValuesAsync();
                if (recentValues != null)
                {
                    model.RecentValues = recentValues.Select(v => new DashboardValue
                    {
                        NodeId = v.NodeId,
                        DisplayName = v.DisplayName,
                        Value = v.Value,
                        StatusCode = v.StatusCode,
                        SourceTimestamp = v.SourceTimestamp
                    }).ToList();
                }
            }

            return View(model);
        }

        public IActionResult Browser()
        {
            return View();
        }

        public IActionResult Monitor()
        {
            return View();
        }

        public IActionResult Settings()
        {
            var model = new SettingsViewModel
            {
                EndpointUrl = _mockOpcUaWebService.ServerUrl,
                IsConnected = _mockOpcUaWebService.IsConnected,
                AutoReconnect = true,
                SecurityPolicy = "None",
                MessageSecurityMode = "None",
                UserIdentityType = "Anonymous"
            };

            return View(model);
        }

        public IActionResult Browser()
        {
            return View();
        }

        public IActionResult Monitor()
        {
            return View();
        }

        public IActionResult Settings()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
