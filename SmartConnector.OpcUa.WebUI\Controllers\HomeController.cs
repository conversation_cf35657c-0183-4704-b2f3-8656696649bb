using Microsoft.AspNetCore.Mvc;
using SmartConnector.OpcUa.WebUI.Models;
using SmartConnector.OpcUa.WebUI.Services;
using System.Diagnostics;

namespace SmartConnector.OpcUa.WebUI.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly OpcUaWebService _opcUaWebService;

        public HomeController(ILogger<HomeController> logger, OpcUaWebService opcUaWebService)
        {
            _logger = logger;
            _opcUaWebService = opcUaWebService;
        }

        public async Task<IActionResult> Index()
        {
            var model = new DashboardViewModel
            {
                IsConnected = _opcUaWebService.IsConnected,
                ConnectionStatus = _opcUaWebService.IsConnected ? "Connected" : "Disconnected",
                ServerUrl = _opcUaWebService.ServerUrl,
                LastUpdate = DateTime.Now
            };

            if (_opcUaWebService.IsConnected)
            {
                model.ServerInfo = await _opcUaWebService.GetServerInfoAsync();
                model.RecentValues = await _opcUaWebService.GetRecentValuesAsync();
            }

            return View(model);
        }

        public IActionResult Browser()
        {
            return View();
        }

        public IActionResult Monitor()
        {
            return View();
        }

        public IActionResult Settings()
        {
            var model = new SettingsViewModel
            {
                EndpointUrl = _opcUaWebService.ServerUrl,
                IsConnected = _opcUaWebService.IsConnected,
                AutoReconnect = true,
                SecurityPolicy = "None",
                MessageSecurityMode = "None",
                UserIdentityType = "Anonymous"
            };

            return View(model);
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
