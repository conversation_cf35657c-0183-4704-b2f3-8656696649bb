using Microsoft.AspNetCore.Mvc;
using SmartConnector.OpcUa.WebUI.Models;
using SmartConnector.OpcUa.WebUI.Services;
using System.Diagnostics;

namespace SmartConnector.OpcUa.WebUI.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly RealOpcUaService _realOpcUaService;

        public HomeController(ILogger<HomeController> logger, RealOpcUaService realOpcUaService)
        {
            _logger = logger;
            _realOpcUaService = realOpcUaService;
        }

        public async Task<IActionResult> Index()
        {
            var model = new DashboardViewModel
            {
                IsConnected = _realOpcUaService.IsConnected,
                ConnectionStatus = _realOpcUaService.IsConnected ? "Connected" : "Disconnected",
                ServerUrl = _realOpcUaService.ServerUrl,
                LastUpdate = DateTime.Now
            };

            if (_realOpcUaService.IsConnected)
            {
                // For now, set basic server info since we're connected to a real server
                model.ServerInfo = new DashboardServerInfo
                {
                    ApplicationName = "Real OPC UA Server",
                    ApplicationUri = _realOpcUaService.ServerUrl ?? "Unknown",
                    ProductUri = "Real Server",
                    SoftwareVersion = "Unknown",
                    BuildNumber = "Unknown",
                    BuildDate = DateTime.Now,
                    ServerCapabilities = new List<string> { "Real OPC UA Operations" },
                    SupportedProfiles = new List<string> { "Standard Profile" }
                };

                // Set some placeholder recent values for real server
                var recentValues = new List<DashboardValue>
                {
                    new DashboardValue { NodeId = "Connected", DisplayName = "Server Status", Value = "Connected", SourceTimestamp = DateTime.Now },
                    new DashboardValue { NodeId = "Ready", DisplayName = "Client Status", Value = "Ready", SourceTimestamp = DateTime.Now }
                };
                model.RecentValues = recentValues;
            }

            return View(model);
        }

        public IActionResult Browser()
        {
            return View();
        }

        public IActionResult Monitor()
        {
            return View();
        }

        public IActionResult Settings()
        {
            var model = new SettingsViewModel
            {
                EndpointUrl = _realOpcUaService.ServerUrl,
                IsConnected = _realOpcUaService.IsConnected,
                AutoReconnect = true,
                SecurityPolicy = "None",
                MessageSecurityMode = "None",
                UserIdentityType = "Anonymous"
            };

            return View(model);
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
