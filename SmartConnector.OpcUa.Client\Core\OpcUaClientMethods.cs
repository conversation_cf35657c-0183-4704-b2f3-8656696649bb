using Microsoft.Extensions.Logging;
using Opc.Ua;
using Opc.Ua.Client;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// OPC UA client methods implementation (partial class)
    /// </summary>
    public partial class OpcUaClient
    {
        /// <summary>
        /// Disconnect from OPC UA server
        /// </summary>
        public async Task<OpcUaOperationResult> DisconnectAsync()
        {
            try
            {
                lock (_lock)
                {
                    if (!IsConnected)
                    {
                        return new OpcUaOperationResult
                        {
                            IsSuccess = true,
                            Message = "Already disconnected"
                        };
                    }
                }

                _logger.LogInformation("Disconnecting from OPC UA server");

                // Stop reconnect timer
                _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);

                // Close session
                if (_session != null)
                {
                    await _session.CloseAsync();
                    _session.Dispose();
                    _session = null;
                }

                // Clear subscriptions
                lock (_lock)
                {
                    _subscriptions.Clear();
                }

                _logger.LogInformation("Successfully disconnected from OPC UA server");
                
                ConnectionStatusChanged?.Invoke(this, new OpcUaConnectionEventArgs 
                { 
                    IsConnected = false 
                });

                return new OpcUaOperationResult
                {
                    IsSuccess = true,
                    Message = "Disconnected successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during disconnect");
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Disconnect failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Reconnect to OPC UA server
        /// </summary>
        public async Task<OpcUaOperationResult> ReconnectAsync()
        {
            try
            {
                _logger.LogInformation("Reconnecting to OPC UA server");

                // Disconnect first
                await DisconnectAsync();

                // Wait a moment
                await Task.Delay(1000);

                // Connect again
                var result = await ConnectAsync();
                
                if (result.IsSuccess)
                {
                    _reconnectAttempts = 0;
                    _logger.LogInformation("Successfully reconnected to OPC UA server");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during reconnect");
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Reconnect failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Browse server nodes
        /// </summary>
        public async Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseAsync(NodeId? startingNode = null, int maxReferencesToReturn = 1000)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<List<OpcUaNode>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var nodeToBrowse = startingNode ?? ObjectIds.ObjectsFolder;
                
                var browseDescription = new BrowseDescription
                {
                    NodeId = nodeToBrowse,
                    BrowseDirection = BrowseDirection.Forward,
                    ReferenceTypeId = ReferenceTypeIds.References,
                    IncludeSubtypes = true,
                    NodeClassMask = 0,
                    ResultMask = (uint)BrowseResultMask.All
                };

                var response = await _session.BrowseAsync(null, null, (uint)maxReferencesToReturn, 
                    new BrowseDescriptionCollection { browseDescription });

                var nodes = new List<OpcUaNode>();

                if (response?.Results?.Count > 0)
                {
                    foreach (var reference in response.Results[0].References)
                    {
                        var node = new OpcUaNode
                        {
                            NodeId = ExpandedNodeId.ToNodeId(reference.NodeId, _session.NamespaceUris),
                            DisplayName = reference.DisplayName?.Text ?? string.Empty,
                            BrowseName = reference.BrowseName?.Name ?? string.Empty,
                            NodeClass = reference.NodeClass
                        };

                        // Read additional attributes
                        await ReadNodeDetailsAsync(node);
                        
                        nodes.Add(node);
                    }
                }

                _logger.LogDebug("Browsed {Count} nodes from {StartingNode}", nodes.Count, nodeToBrowse);

                return new OpcUaOperationResult<List<OpcUaNode>>
                {
                    IsSuccess = true,
                    Message = $"Browsed {nodes.Count} nodes",
                    Data = nodes
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error browsing nodes");
                
                return new OpcUaOperationResult<List<OpcUaNode>>
                {
                    IsSuccess = false,
                    Message = "Browse failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Browse server nodes recursively
        /// </summary>
        public async Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseRecursiveAsync(NodeId? startingNode = null, int maxDepth = 5)
        {
            try
            {
                var allNodes = new List<OpcUaNode>();
                var nodesToBrowse = new Queue<(NodeId nodeId, int depth)>();
                
                nodesToBrowse.Enqueue((startingNode ?? ObjectIds.ObjectsFolder, 0));

                while (nodesToBrowse.Count > 0)
                {
                    var (currentNode, currentDepth) = nodesToBrowse.Dequeue();
                    
                    if (currentDepth >= maxDepth)
                        continue;

                    var browseResult = await BrowseAsync(currentNode);
                    
                    if (browseResult.IsSuccess && browseResult.Data != null)
                    {
                        foreach (var node in browseResult.Data)
                        {
                            allNodes.Add(node);
                            
                            // Add children to browse queue
                            if (node.NodeClass == NodeClass.Object || node.NodeClass == NodeClass.Variable)
                            {
                                nodesToBrowse.Enqueue((node.NodeId, currentDepth + 1));
                            }
                        }
                    }
                }

                _logger.LogDebug("Recursively browsed {Count} nodes with max depth {MaxDepth}", allNodes.Count, maxDepth);

                return new OpcUaOperationResult<List<OpcUaNode>>
                {
                    IsSuccess = true,
                    Message = $"Recursively browsed {allNodes.Count} nodes",
                    Data = allNodes
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during recursive browse");
                
                return new OpcUaOperationResult<List<OpcUaNode>>
                {
                    IsSuccess = false,
                    Message = "Recursive browse failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Read node details
        /// </summary>
        private async Task ReadNodeDetailsAsync(OpcUaNode node)
        {
            try
            {
                if (_session == null) return;

                var attributesToRead = new ReadValueIdCollection
                {
                    new ReadValueId { NodeId = node.NodeId, AttributeId = Attributes.Description },
                    new ReadValueId { NodeId = node.NodeId, AttributeId = Attributes.DataType },
                    new ReadValueId { NodeId = node.NodeId, AttributeId = Attributes.ValueRank },
                    new ReadValueId { NodeId = node.NodeId, AttributeId = Attributes.AccessLevel },
                    new ReadValueId { NodeId = node.NodeId, AttributeId = Attributes.UserAccessLevel }
                };

                var response = await _session.ReadAsync(null, 0, TimestampsToReturn.Neither, attributesToRead);

                if (response?.Results?.Count >= 5)
                {
                    if (StatusCode.IsGood(response.Results[0].StatusCode))
                        node.Description = response.Results[0].Value?.ToString();
                    
                    if (StatusCode.IsGood(response.Results[1].StatusCode))
                        node.DataType = response.Results[1].Value as NodeId;
                    
                    if (StatusCode.IsGood(response.Results[2].StatusCode))
                        node.ValueRank = response.Results[2].Value as int?;
                    
                    if (StatusCode.IsGood(response.Results[3].StatusCode))
                        node.AccessLevel = (byte)(response.Results[3].Value ?? 0);
                    
                    if (StatusCode.IsGood(response.Results[4].StatusCode))
                        node.UserAccessLevel = (byte)(response.Results[4].Value ?? 0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error reading node details for {NodeId}", node.NodeId);
            }
        }

        /// <summary>
        /// Read node values
        /// </summary>
        public async Task<OpcUaOperationResult<List<OpcUaValue>>> ReadAsync(List<NodeId> nodeIds)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<List<OpcUaValue>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var nodesToRead = nodeIds.Select(nodeId => new ReadValueId
                {
                    NodeId = nodeId,
                    AttributeId = Attributes.Value
                }).ToList();

                var response = await _session.ReadAsync(null, 0, TimestampsToReturn.Both, 
                    new ReadValueIdCollection(nodesToRead));

                var values = new List<OpcUaValue>();

                if (response?.Results != null)
                {
                    for (int i = 0; i < response.Results.Count && i < nodeIds.Count; i++)
                    {
                        var result = response.Results[i];
                        
                        values.Add(new OpcUaValue
                        {
                            NodeId = nodeIds[i],
                            Value = result.Value,
                            StatusCode = result.StatusCode,
                            SourceTimestamp = result.SourceTimestamp,
                            ServerTimestamp = result.ServerTimestamp
                        });
                    }
                }

                _logger.LogDebug("Read {Count} node values", values.Count);

                return new OpcUaOperationResult<List<OpcUaValue>>
                {
                    IsSuccess = true,
                    Message = $"Read {values.Count} values",
                    Data = values
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading node values");
                
                return new OpcUaOperationResult<List<OpcUaValue>>
                {
                    IsSuccess = false,
                    Message = "Read failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Read single node value
        /// </summary>
        public async Task<OpcUaOperationResult<OpcUaValue>> ReadAsync(NodeId nodeId)
        {
            var result = await ReadAsync(new List<NodeId> { nodeId });
            
            if (result.IsSuccess && result.Data?.Count > 0)
            {
                return new OpcUaOperationResult<OpcUaValue>
                {
                    IsSuccess = true,
                    Message = result.Message,
                    Data = result.Data[0]
                };
            }

            return new OpcUaOperationResult<OpcUaValue>
            {
                IsSuccess = false,
                Message = result.Message,
                ErrorDetails = result.ErrorDetails,
                Exception = result.Exception
            };
        }
    }
}
