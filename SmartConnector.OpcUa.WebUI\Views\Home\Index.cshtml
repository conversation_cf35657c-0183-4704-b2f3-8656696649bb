@model SmartConnector.OpcUa.WebUI.Models.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> OPC UA Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Connection Status Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="connection-status @(Model.IsConnected ? "connected" : "disconnected")">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-1">
                        <i class="fas fa-@(Model.IsConnected ? "check-circle" : "times-circle")"></i>
                        Connection Status: @Model.ConnectionStatus
                    </h5>
                    <p class="mb-0">Server: @Model.ServerUrl</p>
                    @if (Model.IsConnected && Model.ServerInfo != null)
                    {
                        <small>Application: @Model.ServerInfo.ApplicationName | Version: @Model.ServerInfo.SoftwareVersion</small>
                    }
                </div>
                <div class="col-md-4 text-end">
                    <small class="text-muted">Last Update: @Model.LastUpdate.ToString("HH:mm:ss")</small>
                </div>
            </div>
        </div>
    </div>
</div>

@if (Model.IsConnected)
{
    <!-- Server Information -->
    @if (Model.ServerInfo != null)
    {
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-server"></i> Server Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Application Name:</strong></td>
                                <td>@Model.ServerInfo.ApplicationName</td>
                            </tr>
                            <tr>
                                <td><strong>Application URI:</strong></td>
                                <td><small>@Model.ServerInfo.ApplicationUri</small></td>
                            </tr>
                            <tr>
                                <td><strong>Product URI:</strong></td>
                                <td><small>@Model.ServerInfo.ProductUri</small></td>
                            </tr>
                            <tr>
                                <td><strong>Software Version:</strong></td>
                                <td>@Model.ServerInfo.SoftwareVersion</td>
                            </tr>
                            <tr>
                                <td><strong>Build Number:</strong></td>
                                <td>@Model.ServerInfo.BuildNumber</td>
                            </tr>
                            <tr>
                                <td><strong>Build Date:</strong></td>
                                <td>@Model.ServerInfo.BuildDate.ToString("yyyy-MM-dd")</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-shield-alt"></i> Security & Capabilities</h5>
                    </div>
                    <div class="card-body">
                        <h6>Server Capabilities:</h6>
                        @if (Model.ServerInfo.ServerCapabilities?.Any() == true)
                        {
                            <ul class="list-unstyled">
                                @foreach (var capability in Model.ServerInfo.ServerCapabilities)
                                {
                                    <li><i class="fas fa-check text-success"></i> @capability</li>
                                }
                            </ul>
                        }
                        else
                        {
                            <p class="text-muted">No capabilities information available</p>
                        }
                        
                        <h6 class="mt-3">Supported Profiles:</h6>
                        @if (Model.ServerInfo.SupportedProfiles?.Any() == true)
                        {
                            <ul class="list-unstyled">
                                @foreach (var profile in Model.ServerInfo.SupportedProfiles)
                                {
                                    <li><i class="fas fa-check text-success"></i> @profile</li>
                                }
                            </ul>
                        }
                        else
                        {
                            <p class="text-muted">No profile information available</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Recent Values -->
    @if (Model.RecentValues?.Any() == true)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-line"></i> Recent Values</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshValues()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var value in Model.RecentValues)
                            {
                                <div class="col-md-4 mb-3">
                                    <div class="value-card">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">@(value.DisplayName ?? value.NodeId.ToString())</h6>
                                                <p class="mb-1 h5 text-primary">@(value.Value?.ToString() ?? "N/A")</p>
                                                <small class="text-muted">@value.NodeId</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="status-indicator @GetStatusClass(value.StatusCode.ToString())"></span>
                                                <br>
                                                <small class="text-muted">@value.SourceTimestamp.ToString("HH:mm:ss")</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="@Url.Action("Browser", "Home")" class="btn btn-outline-primary w-100">
                                <i class="fas fa-sitemap"></i><br>
                                Browse Nodes
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="@Url.Action("Monitor", "Home")" class="btn btn-outline-success w-100">
                                <i class="fas fa-chart-line"></i><br>
                                Real-time Monitor
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="readServerTime()">
                                <i class="fas fa-clock"></i><br>
                                Read Server Time
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="getServerStatus()">
                                <i class="fas fa-heartbeat"></i><br>
                                Server Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Node Operations -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-tools"></i> Custom Node Operations</h5>
                </div>
                <div class="card-body">
                    <!-- Node ID Input -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="customNodeId" class="form-label">Node ID</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="customNodeId"
                                       placeholder="Enter Node ID (e.g., ns=2;s=Temperature)"
                                       value="ns=2;s=Temperature">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-list"></i> Presets
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=2;s=Temperature')">Temperature Sensor</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=2;s=Pressure')">Pressure Sensor</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=2;s=FlowRate')">Flow Rate Sensor</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=0;i=2258')">Server Status</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=0;i=2256')">Server Time</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=0;i=2267')">Server State</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=2;s=Motor1.Speed')">Motor Speed</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=2;s=Valve1.Position')">Valve Position</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setNodeId('ns=2;s=Tank1.Level')">Tank Level</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="customDisplayName" class="form-label">Display Name (Optional)</label>
                            <input type="text" class="form-control" id="customDisplayName"
                                   placeholder="Custom display name">
                        </div>
                    </div>

                    <!-- Operation Buttons -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" onclick="readCustomNode()">
                                    <i class="fas fa-eye"></i> Read Value
                                </button>
                                <button type="button" class="btn btn-success" onclick="subscribeToCustomNode()">
                                    <i class="fas fa-bell"></i> Subscribe
                                </button>
                                <button type="button" class="btn btn-warning" onclick="writeCustomNode()">
                                    <i class="fas fa-edit"></i> Write Value
                                </button>
                                <button type="button" class="btn btn-info" onclick="browseCustomNode()">
                                    <i class="fas fa-search"></i> Browse Node
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Write Value Section (Initially Hidden) -->
                    <div class="row mb-3" id="writeValueSection" style="display: none;">
                        <div class="col-md-6">
                            <label for="writeValue" class="form-label">Value to Write</label>
                            <input type="text" class="form-control" id="writeValue"
                                   placeholder="Enter value to write">
                        </div>
                        <div class="col-md-3">
                            <label for="writeDataType" class="form-label">Data Type</label>
                            <select class="form-select" id="writeDataType">
                                <option value="String">String</option>
                                <option value="Int32">Integer</option>
                                <option value="Double" selected>Double</option>
                                <option value="Boolean">Boolean</option>
                                <option value="DateTime">DateTime</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-success" onclick="executeWrite()">
                                    <i class="fas fa-check"></i> Execute Write
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="cancelWrite()">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Display -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0"><i class="fas fa-clipboard-list"></i> Operation Results</h6>
                                </div>
                                <div class="card-body">
                                    <div id="customNodeResults">
                                        <p class="text-muted mb-0">No operations performed yet. Select a Node ID and choose an operation.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <!-- Not Connected State -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-unlink fa-3x text-muted mb-3"></i>
                    <h4>Not Connected to OPC UA Server</h4>
                    <p class="text-muted">Connect to an OPC UA server to start monitoring and browsing nodes.</p>
                    <button class="btn btn-primary btn-lg" onclick="connectToServer()">
                        <i class="fas fa-plug"></i> Connect to Server
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@functions {
    private string GetStatusClass(string statusCode)
    {
        if (statusCode.Contains("Good")) return "status-good";
        if (statusCode.Contains("Bad")) return "status-bad";
        return "status-uncertain";
    }
}

<script>
    function refreshDashboard() {
        location.reload();
    }

    function refreshValues() {
        // Implement real-time value refresh
        fetch('/api/opcua/status')
            .then(response => response.json())
            .then(data => {
                if (data.isConnected) {
                    location.reload();
                }
            });
    }

    function readServerTime() {
        fetch('/api/opcua/read', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nodeIds: ['i=2258'] // Server_ServerStatus_CurrentTime
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.Success && data.Data.length > 0) {
                alert('Server Time: ' + data.Data[0].Value);
            } else {
                alert('Failed to read server time');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reading server time');
        });
    }

    function getServerStatus() {
        fetch('/api/opcua/read', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nodeIds: ['i=2256'] // Server_ServerStatus_State
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.Success && data.Data.length > 0) {
                alert('Server State: ' + data.Data[0].Value);
            } else {
                alert('Failed to read server status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reading server status');
        });
    }

    // Custom Node Operations Functions
    function setNodeId(nodeId) {
        document.getElementById('customNodeId').value = nodeId;

        // Set appropriate display names for common nodes
        const displayNameMap = {
            'ns=2;s=Temperature': 'Temperature Sensor',
            'ns=2;s=Pressure': 'Pressure Sensor',
            'ns=2;s=FlowRate': 'Flow Rate Sensor',
            'ns=0;i=2258': 'Server Status',
            'ns=0;i=2256': 'Server Time',
            'ns=0;i=2267': 'Server State',
            'ns=2;s=Motor1.Speed': 'Motor 1 Speed',
            'ns=2;s=Valve1.Position': 'Valve 1 Position',
            'ns=2;s=Tank1.Level': 'Tank 1 Level'
        };

        const displayName = displayNameMap[nodeId] || '';
        document.getElementById('customDisplayName').value = displayName;
    }

    function readCustomNode() {
        const nodeId = document.getElementById('customNodeId').value.trim();
        if (!nodeId) {
            alert('Please enter a Node ID');
            return;
        }

        showOperationInProgress('Reading node value...');

        fetch('/api/opcua/read', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nodeIds: [nodeId]
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.Success && data.Data.length > 0) {
                const result = data.Data[0];
                showOperationResult('Read Operation', {
                    nodeId: nodeId,
                    value: result.Value,
                    dataType: result.DataType || 'Unknown',
                    statusCode: result.StatusCode || 'Good',
                    timestamp: new Date().toLocaleString()
                });
            } else {
                showOperationError('Failed to read node value: ' + (data.Message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showOperationError('Error reading node: ' + error.message);
        });
    }

    function subscribeToCustomNode() {
        const nodeId = document.getElementById('customNodeId').value.trim();
        if (!nodeId) {
            alert('Please enter a Node ID');
            return;
        }

        showOperationInProgress('Creating subscription...');

        fetch('/api/opcua/subscribe', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nodeIds: [nodeId],
                publishingInterval: 1000
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.Success) {
                showOperationResult('Subscribe Operation', {
                    nodeId: nodeId,
                    subscriptionId: data.SubscriptionId,
                    message: 'Subscription created successfully. You will receive real-time updates.',
                    timestamp: new Date().toLocaleString()
                });
            } else {
                showOperationError('Failed to create subscription: ' + (data.Message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showOperationError('Error creating subscription: ' + error.message);
        });
    }

    function writeCustomNode() {
        const nodeId = document.getElementById('customNodeId').value.trim();
        if (!nodeId) {
            alert('Please enter a Node ID');
            return;
        }

        // Show the write value section
        document.getElementById('writeValueSection').style.display = 'block';
        document.getElementById('writeValue').focus();
    }

    function executeWrite() {
        const nodeId = document.getElementById('customNodeId').value.trim();
        const value = document.getElementById('writeValue').value.trim();
        const dataType = document.getElementById('writeDataType').value;

        if (!nodeId || !value) {
            alert('Please enter both Node ID and value to write');
            return;
        }

        showOperationInProgress('Writing value to node...');

        fetch('/api/opcua/write-single', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nodeId: nodeId,
                value: value,
                dataType: dataType
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOperationResult('Write Operation', {
                    nodeId: nodeId,
                    writtenValue: value,
                    dataType: dataType,
                    statusCode: data.statusCode || 'Good',
                    message: 'Value written successfully',
                    timestamp: new Date().toLocaleString()
                });
            } else {
                showOperationError('Failed to write value: ' + (data.message || 'Unknown error'));
            }
            cancelWrite();
        })
        .catch(error => {
            console.error('Error:', error);
            showOperationError('Error writing value: ' + error.message);
            cancelWrite();
        });
    }

    function cancelWrite() {
        document.getElementById('writeValueSection').style.display = 'none';
        document.getElementById('writeValue').value = '';
    }

    function browseCustomNode() {
        const nodeId = document.getElementById('customNodeId').value.trim();
        if (!nodeId) {
            alert('Please enter a Node ID');
            return;
        }

        showOperationInProgress('Browsing node...');

        fetch('/api/opcua/browse-node', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nodeId: nodeId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                showBrowseResult(nodeId, data.data);
            } else {
                showOperationError('Failed to browse node: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showOperationError('Error browsing node: ' + error.message);
        });
    }

    // Helper functions for displaying results
    function showOperationInProgress(message) {
        const resultsDiv = document.getElementById('customNodeResults');
        resultsDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span>${message}</span>
            </div>
        `;
    }

    function showOperationResult(operation, data) {
        const resultsDiv = document.getElementById('customNodeResults');
        let content = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle"></i> ${operation} Successful</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Node ID:</strong> <code>${data.nodeId}</code><br>
        `;

        if (data.value !== undefined) {
            content += `<strong>Value:</strong> <span class="text-primary">${data.value}</span><br>`;
        }
        if (data.writtenValue !== undefined) {
            content += `<strong>Written Value:</strong> <span class="text-success">${data.writtenValue}</span><br>`;
        }
        if (data.dataType) {
            content += `<strong>Data Type:</strong> ${data.dataType}<br>`;
        }
        if (data.statusCode) {
            content += `<strong>Status:</strong> <span class="badge bg-success">${data.statusCode}</span><br>`;
        }
        if (data.subscriptionId) {
            content += `<strong>Subscription ID:</strong> ${data.subscriptionId}<br>`;
        }

        content += `
                    </div>
                    <div class="col-md-6">
                        <strong>Timestamp:</strong> ${data.timestamp}<br>
        `;

        if (data.message) {
            content += `<strong>Message:</strong> ${data.message}<br>`;
        }

        content += `
                    </div>
                </div>
            </div>
        `;

        resultsDiv.innerHTML = content;
    }

    function showOperationError(message) {
        const resultsDiv = document.getElementById('customNodeResults');
        resultsDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Operation Failed</h6>
                <p class="mb-0">${message}</p>
                <small class="text-muted">Timestamp: ${new Date().toLocaleString()}</small>
            </div>
        `;
    }

    function showBrowseResult(nodeId, browseData) {
        const resultsDiv = document.getElementById('customNodeResults');
        let content = `
            <div class="alert alert-info">
                <h6><i class="fas fa-sitemap"></i> Browse Results for: <code>${nodeId}</code></h6>
        `;

        if (browseData.children && browseData.children.length > 0) {
            content += `
                <div class="mt-3">
                    <strong>Child Nodes (${browseData.children.length}):</strong>
                    <div class="list-group mt-2">
            `;

            browseData.children.forEach(child => {
                content += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${child.displayName || child.browseName}</strong><br>
                                <small class="text-muted">${child.nodeId}</small><br>
                                <span class="badge bg-secondary">${child.nodeClass || 'Unknown'}</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="setNodeId('${child.nodeId}')">
                                <i class="fas fa-arrow-up"></i> Select
                            </button>
                        </div>
                    </div>
                `;
            });

            content += `
                    </div>
                </div>
            `;
        } else {
            content += `<p class="mt-2 text-muted">No child nodes found.</p>`;
        }

        if (browseData.attributes) {
            content += `
                <div class="mt-3">
                    <strong>Node Attributes:</strong>
                    <ul class="list-unstyled mt-2">
            `;

            Object.keys(browseData.attributes).forEach(key => {
                content += `<li><strong>${key}:</strong> ${browseData.attributes[key]}</li>`;
            });

            content += `
                    </ul>
                </div>
            `;
        }

        content += `
                <small class="text-muted">Timestamp: ${new Date().toLocaleString()}</small>
            </div>
        `;

        resultsDiv.innerHTML = content;
    }
</script>
