@model SmartConnector.OpcUa.WebUI.Models.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> OPC UA Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Connection Status Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="connection-status @(Model.IsConnected ? "connected" : "disconnected")">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-1">
                        <i class="fas fa-@(Model.IsConnected ? "check-circle" : "times-circle")"></i>
                        Connection Status: @Model.ConnectionStatus
                    </h5>
                    <p class="mb-0">Server: @Model.ServerUrl</p>
                    @if (Model.IsConnected && Model.ServerInfo != null)
                    {
                        <small>Application: @Model.ServerInfo.ApplicationName | Version: @Model.ServerInfo.SoftwareVersion</small>
                    }
                </div>
                <div class="col-md-4 text-end">
                    <small class="text-muted">Last Update: @Model.LastUpdate.ToString("HH:mm:ss")</small>
                </div>
            </div>
        </div>
    </div>
</div>

@if (Model.IsConnected)
{
    <!-- Server Information -->
    @if (Model.ServerInfo != null)
    {
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-server"></i> Server Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Application Name:</strong></td>
                                <td>@Model.ServerInfo.ApplicationName</td>
                            </tr>
                            <tr>
                                <td><strong>Application URI:</strong></td>
                                <td><small>@Model.ServerInfo.ApplicationUri</small></td>
                            </tr>
                            <tr>
                                <td><strong>Product URI:</strong></td>
                                <td><small>@Model.ServerInfo.ProductUri</small></td>
                            </tr>
                            <tr>
                                <td><strong>Software Version:</strong></td>
                                <td>@Model.ServerInfo.SoftwareVersion</td>
                            </tr>
                            <tr>
                                <td><strong>Build Number:</strong></td>
                                <td>@Model.ServerInfo.BuildNumber</td>
                            </tr>
                            <tr>
                                <td><strong>Build Date:</strong></td>
                                <td>@Model.ServerInfo.BuildDate.ToString("yyyy-MM-dd")</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-shield-alt"></i> Security & Capabilities</h5>
                    </div>
                    <div class="card-body">
                        <h6>Server Capabilities:</h6>
                        @if (Model.ServerInfo.ServerCapabilities?.Any() == true)
                        {
                            <ul class="list-unstyled">
                                @foreach (var capability in Model.ServerInfo.ServerCapabilities)
                                {
                                    <li><i class="fas fa-check text-success"></i> @capability</li>
                                }
                            </ul>
                        }
                        else
                        {
                            <p class="text-muted">No capabilities information available</p>
                        }
                        
                        <h6 class="mt-3">Supported Profiles:</h6>
                        @if (Model.ServerInfo.SupportedProfiles?.Any() == true)
                        {
                            <ul class="list-unstyled">
                                @foreach (var profile in Model.ServerInfo.SupportedProfiles)
                                {
                                    <li><i class="fas fa-check text-success"></i> @profile</li>
                                }
                            </ul>
                        }
                        else
                        {
                            <p class="text-muted">No profile information available</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Recent Values -->
    @if (Model.RecentValues?.Any() == true)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-line"></i> Recent Values</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshValues()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var value in Model.RecentValues)
                            {
                                <div class="col-md-4 mb-3">
                                    <div class="value-card">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">@(value.DisplayName ?? value.NodeId.ToString())</h6>
                                                <p class="mb-1 h5 text-primary">@(value.Value?.ToString() ?? "N/A")</p>
                                                <small class="text-muted">@value.NodeId</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="status-indicator @GetStatusClass(value.StatusCode.ToString())"></span>
                                                <br>
                                                <small class="text-muted">@value.SourceTimestamp.ToString("HH:mm:ss")</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="@Url.Action("Browser", "Home")" class="btn btn-outline-primary w-100">
                                <i class="fas fa-sitemap"></i><br>
                                Browse Nodes
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="@Url.Action("Monitor", "Home")" class="btn btn-outline-success w-100">
                                <i class="fas fa-chart-line"></i><br>
                                Real-time Monitor
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="readServerTime()">
                                <i class="fas fa-clock"></i><br>
                                Read Server Time
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="getServerStatus()">
                                <i class="fas fa-heartbeat"></i><br>
                                Server Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <!-- Not Connected State -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-unlink fa-3x text-muted mb-3"></i>
                    <h4>Not Connected to OPC UA Server</h4>
                    <p class="text-muted">Connect to an OPC UA server to start monitoring and browsing nodes.</p>
                    <button class="btn btn-primary btn-lg" onclick="connectToServer()">
                        <i class="fas fa-plug"></i> Connect to Server
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@functions {
    private string GetStatusClass(string statusCode)
    {
        if (statusCode.Contains("Good")) return "status-good";
        if (statusCode.Contains("Bad")) return "status-bad";
        return "status-uncertain";
    }
}

<script>
    function refreshDashboard() {
        location.reload();
    }

    function refreshValues() {
        // Implement real-time value refresh
        fetch('/api/opcua/status')
            .then(response => response.json())
            .then(data => {
                if (data.isConnected) {
                    location.reload();
                }
            });
    }

    function readServerTime() {
        fetch('/api/opcua/read', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                nodeIds: ['i=2258'] // Server_ServerStatus_CurrentTime
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                alert('Server Time: ' + data.data[0].value);
            } else {
                alert('Failed to read server time');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reading server time');
        });
    }

    function getServerStatus() {
        fetch('/api/opcua/read', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                nodeIds: ['i=2256'] // Server_ServerStatus_State
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                alert('Server State: ' + data.data[0].value);
            } else {
                alert('Failed to read server status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reading server status');
        });
    }
</script>
