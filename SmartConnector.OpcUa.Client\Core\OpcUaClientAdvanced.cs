using Microsoft.Extensions.Logging;
using Opc.Ua;
using Opc.Ua.Client;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// OPC UA client advanced operations implementation (partial class)
    /// </summary>
    public partial class OpcUaClient
    {
        /// <summary>
        /// Get server endpoints
        /// </summary>
        public async Task<OpcUaOperationResult<List<EndpointDescription>>> GetEndpointsAsync()
        {
            try
            {
                var endpointConfiguration = EndpointConfiguration.Create(_applicationConfiguration);
                var endpoints = await DiscoveryClient.GetEndpointsAsync(_config.EndpointUrl, endpointConfiguration);

                _logger.LogDebug("Retrieved {Count} endpoints from server", endpoints.Count);

                return new OpcUaOperationResult<List<EndpointDescription>>
                {
                    IsSuccess = true,
                    Message = $"Retrieved {endpoints.Count} endpoints",
                    Data = endpoints.ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server endpoints");
                
                return new OpcUaOperationResult<List<EndpointDescription>>
                {
                    IsSuccess = false,
                    Message = "Failed to get endpoints",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Get server status
        /// </summary>
        public async Task<OpcUaOperationResult<OpcUaServerStatus>> GetServerStatusAsync()
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<OpcUaServerStatus>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var serverStatusNode = VariableIds.Server_ServerStatus;
                var readResult = await ReadAsync(serverStatusNode);

                if (readResult.IsSuccess && readResult.Data?.Value is ExtensionObject extensionObject)
                {
                    if (extensionObject.Body is ServerStatusDataType serverStatus)
                    {
                        var status = new OpcUaServerStatus
                        {
                            StartTime = serverStatus.StartTime,
                            CurrentTime = serverStatus.CurrentTime,
                            State = serverStatus.State,
                            BuildInfo = serverStatus.BuildInfo?.ToString() ?? string.Empty,
                            SecondsTillShutdown = serverStatus.SecondsTillShutdown,
                            ShutdownReason = serverStatus.ShutdownReason
                        };

                        return new OpcUaOperationResult<OpcUaServerStatus>
                        {
                            IsSuccess = true,
                            Message = "Server status retrieved successfully",
                            Data = status
                        };
                    }
                }

                return new OpcUaOperationResult<OpcUaServerStatus>
                {
                    IsSuccess = false,
                    Message = "Failed to parse server status"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server status");
                
                return new OpcUaOperationResult<OpcUaServerStatus>
                {
                    IsSuccess = false,
                    Message = "Failed to get server status",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Get server information
        /// </summary>
        public async Task<OpcUaOperationResult<OpcUaServerInfo>> GetServerInfoAsync()
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<OpcUaServerInfo>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var serverInfo = new OpcUaServerInfo();

                // Read server array
                var serverArrayResult = await ReadAsync(VariableIds.Server_ServerArray);
                if (serverArrayResult.IsSuccess && serverArrayResult.Data?.Value is string[] serverArray)
                {
                    serverInfo.ApplicationUri = serverArray.FirstOrDefault() ?? string.Empty;
                }

                // Read namespace array
                var namespaceArrayResult = await ReadAsync(VariableIds.Server_NamespaceArray);
                if (namespaceArrayResult.IsSuccess && namespaceArrayResult.Data?.Value is string[] namespaceArray)
                {
                    // Process namespace information if needed
                }

                // Read server capabilities
                var serverCapabilitiesResult = await ReadAsync(ObjectIds.Server_ServerCapabilities);
                if (serverCapabilitiesResult.IsSuccess)
                {
                    // Browse server capabilities
                    var capabilitiesBrowse = await BrowseAsync(ObjectIds.Server_ServerCapabilities);
                    if (capabilitiesBrowse.IsSuccess && capabilitiesBrowse.Data != null)
                    {
                        serverInfo.ServerCapabilities = capabilitiesBrowse.Data.Select(n => n.DisplayName).ToList();
                    }
                }

                // Get build info
                var buildInfoResult = await ReadAsync(VariableIds.Server_ServerStatus_BuildInfo);
                if (buildInfoResult.IsSuccess && buildInfoResult.Data?.Value is ExtensionObject buildInfoExt)
                {
                    if (buildInfoExt.Body is BuildInfo buildInfo)
                    {
                        serverInfo.ApplicationName = buildInfo.ProductName ?? string.Empty;
                        serverInfo.ProductUri = buildInfo.ProductUri ?? string.Empty;
                        serverInfo.SoftwareVersion = buildInfo.SoftwareVersion ?? string.Empty;
                        serverInfo.BuildNumber = buildInfo.BuildNumber ?? string.Empty;
                        serverInfo.BuildDate = buildInfo.BuildDate;
                    }
                }

                return new OpcUaOperationResult<OpcUaServerInfo>
                {
                    IsSuccess = true,
                    Message = "Server information retrieved successfully",
                    Data = serverInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server information");
                
                return new OpcUaOperationResult<OpcUaServerInfo>
                {
                    IsSuccess = false,
                    Message = "Failed to get server information",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Read node attributes
        /// </summary>
        public async Task<OpcUaOperationResult<OpcUaNodeAttributes>> ReadNodeAttributesAsync(NodeId nodeId)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<OpcUaNodeAttributes>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var attributesToRead = new ReadValueIdCollection();
                
                // Add all possible attributes
                var attributeIds = new uint[]
                {
                    Attributes.NodeId, Attributes.NodeClass, Attributes.BrowseName, Attributes.DisplayName,
                    Attributes.Description, Attributes.WriteMask, Attributes.UserWriteMask, Attributes.IsAbstract,
                    Attributes.Symmetric, Attributes.InverseName, Attributes.ContainsNoLoops, Attributes.EventNotifier,
                    Attributes.Value, Attributes.DataType, Attributes.ValueRank, Attributes.ArrayDimensions,
                    Attributes.AccessLevel, Attributes.UserAccessLevel, Attributes.MinimumSamplingInterval,
                    Attributes.Historizing, Attributes.Executable, Attributes.UserExecutable
                };

                foreach (var attributeId in attributeIds)
                {
                    attributesToRead.Add(new ReadValueId
                    {
                        NodeId = nodeId,
                        AttributeId = attributeId
                    });
                }

                var response = await _session.ReadAsync(null, 0, TimestampsToReturn.Neither, attributesToRead);

                var attributes = new OpcUaNodeAttributes { NodeId = nodeId };

                if (response?.Results != null)
                {
                    for (int i = 0; i < response.Results.Count && i < attributeIds.Length; i++)
                    {
                        var result = response.Results[i];
                        if (StatusCode.IsGood(result.StatusCode))
                        {
                            switch (attributeIds[i])
                            {
                                case Attributes.NodeClass:
                                    attributes.NodeClass = (NodeClass)(result.Value ?? NodeClass.Unspecified);
                                    break;
                                case Attributes.BrowseName:
                                    attributes.BrowseName = result.Value as QualifiedName ?? QualifiedName.Null;
                                    break;
                                case Attributes.DisplayName:
                                    attributes.DisplayName = result.Value as LocalizedText ?? LocalizedText.Null;
                                    break;
                                case Attributes.Description:
                                    attributes.Description = result.Value as LocalizedText;
                                    break;
                                case Attributes.WriteMask:
                                    attributes.WriteMask = (uint)(result.Value ?? 0);
                                    break;
                                case Attributes.UserWriteMask:
                                    attributes.UserWriteMask = (uint)(result.Value ?? 0);
                                    break;
                                case Attributes.IsAbstract:
                                    attributes.IsAbstract = (bool)(result.Value ?? false);
                                    break;
                                case Attributes.Symmetric:
                                    attributes.Symmetric = (bool)(result.Value ?? false);
                                    break;
                                case Attributes.InverseName:
                                    attributes.InverseName = result.Value as LocalizedText;
                                    break;
                                case Attributes.ContainsNoLoops:
                                    attributes.ContainsNoLoops = (bool)(result.Value ?? false);
                                    break;
                                case Attributes.EventNotifier:
                                    attributes.EventNotifier = (byte)(result.Value ?? 0);
                                    break;
                                case Attributes.Value:
                                    attributes.Value = result.Value;
                                    break;
                                case Attributes.DataType:
                                    attributes.DataType = result.Value as NodeId;
                                    break;
                                case Attributes.ValueRank:
                                    attributes.ValueRank = (int)(result.Value ?? -1);
                                    break;
                                case Attributes.ArrayDimensions:
                                    attributes.ArrayDimensions = result.Value as uint[];
                                    break;
                                case Attributes.AccessLevel:
                                    attributes.AccessLevel = (byte)(result.Value ?? 0);
                                    break;
                                case Attributes.UserAccessLevel:
                                    attributes.UserAccessLevel = (byte)(result.Value ?? 0);
                                    break;
                                case Attributes.MinimumSamplingInterval:
                                    attributes.MinimumSamplingInterval = (double)(result.Value ?? 0);
                                    break;
                                case Attributes.Historizing:
                                    attributes.Historizing = (bool)(result.Value ?? false);
                                    break;
                                case Attributes.Executable:
                                    attributes.Executable = (bool)(result.Value ?? false);
                                    break;
                                case Attributes.UserExecutable:
                                    attributes.UserExecutable = (bool)(result.Value ?? false);
                                    break;
                            }
                        }
                    }
                }

                return new OpcUaOperationResult<OpcUaNodeAttributes>
                {
                    IsSuccess = true,
                    Message = "Node attributes read successfully",
                    Data = attributes
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading node attributes for {NodeId}", nodeId);
                
                return new OpcUaOperationResult<OpcUaNodeAttributes>
                {
                    IsSuccess = false,
                    Message = "Failed to read node attributes",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Read node history
        /// </summary>
        public async Task<OpcUaOperationResult<List<OpcUaHistoryValue>>> ReadHistoryAsync(NodeId nodeId, DateTime startTime, DateTime endTime, uint maxValues = 1000)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<List<OpcUaHistoryValue>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var historyReadDetails = new ReadRawModifiedDetails
                {
                    StartTime = startTime,
                    EndTime = endTime,
                    NumValuesPerNode = maxValues,
                    IsReadModified = false,
                    ReturnBounds = false
                };

                var nodesToRead = new HistoryReadValueIdCollection
                {
                    new HistoryReadValueId
                    {
                        NodeId = nodeId
                    }
                };

                var response = await _session.HistoryReadAsync(null, new ExtensionObject(historyReadDetails), 
                    TimestampsToReturn.Both, false, nodesToRead);

                var historyValues = new List<OpcUaHistoryValue>();

                if (response?.Results?.Count > 0)
                {
                    var result = response.Results[0];
                    
                    if (StatusCode.IsGood(result.StatusCode) && result.HistoryData is HistoryData historyData)
                    {
                        foreach (var dataValue in historyData.DataValues)
                        {
                            historyValues.Add(new OpcUaHistoryValue
                            {
                                Value = dataValue.Value,
                                StatusCode = dataValue.StatusCode,
                                SourceTimestamp = dataValue.SourceTimestamp,
                                ServerTimestamp = dataValue.ServerTimestamp
                            });
                        }
                    }
                }

                _logger.LogDebug("Read {Count} history values for node {NodeId}", historyValues.Count, nodeId);

                return new OpcUaOperationResult<List<OpcUaHistoryValue>>
                {
                    IsSuccess = true,
                    Message = $"Read {historyValues.Count} history values",
                    Data = historyValues
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading history for node {NodeId}", nodeId);
                
                return new OpcUaOperationResult<List<OpcUaHistoryValue>>
                {
                    IsSuccess = false,
                    Message = "Failed to read history",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Subscribe to events
        /// </summary>
        public async Task<OpcUaOperationResult<uint>> SubscribeToEventsAsync(NodeId? sourceNode = null, List<NodeId>? eventTypes = null)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<uint>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var subscriptionSettings = new OpcUaSubscriptionSettings
                {
                    DisplayName = "Event Subscription",
                    PublishingInterval = 1000
                };

                var subscriptionResult = await CreateSubscriptionAsync(subscriptionSettings);
                
                if (!subscriptionResult.IsSuccess || !subscriptionResult.Data.HasValue)
                {
                    return new OpcUaOperationResult<uint>
                    {
                        IsSuccess = false,
                        Message = "Failed to create event subscription"
                    };
                }

                var subscriptionId = subscriptionResult.Data.Value;

                // Create event monitored item
                var eventItem = new OpcUaMonitoredItem
                {
                    ClientHandle = 1,
                    NodeId = sourceNode ?? ObjectIds.Server,
                    AttributeId = Attributes.EventNotifier,
                    MonitoringMode = MonitoringMode.Reporting,
                    SamplingInterval = 0,
                    QueueSize = 1000,
                    DiscardOldest = true,
                    Filter = new EventFilter
                    {
                        SelectClauses = new SimpleAttributeOperandCollection
                        {
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.EventId } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.EventType } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.SourceNode } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.SourceName } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.Time } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.ReceiveTime } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.Message } },
                            new SimpleAttributeOperand { TypeDefinitionId = ObjectTypeIds.BaseEventType, BrowsePath = new QualifiedNameCollection { BrowseNames.Severity } }
                        }
                    }
                };

                var addItemsResult = await AddMonitoredItemsAsync(subscriptionId, new List<OpcUaMonitoredItem> { eventItem });
                
                if (!addItemsResult.IsSuccess)
                {
                    await DeleteSubscriptionAsync(subscriptionId);
                    return new OpcUaOperationResult<uint>
                    {
                        IsSuccess = false,
                        Message = "Failed to add event monitored item"
                    };
                }

                _logger.LogInformation("Successfully subscribed to events on node {SourceNode}", sourceNode ?? ObjectIds.Server);

                return new OpcUaOperationResult<uint>
                {
                    IsSuccess = true,
                    Message = "Event subscription created successfully",
                    Data = subscriptionId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing to events");
                
                return new OpcUaOperationResult<uint>
                {
                    IsSuccess = false,
                    Message = "Failed to subscribe to events",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Register custom data type
        /// </summary>
        public async Task<OpcUaOperationResult> RegisterCustomDataTypeAsync(Type customType)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                // This is a placeholder for custom data type registration
                // Implementation would depend on the specific custom type and server capabilities
                
                _logger.LogInformation("Custom data type registration requested for type {TypeName}", customType.Name);

                return new OpcUaOperationResult
                {
                    IsSuccess = true,
                    Message = "Custom data type registration completed"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering custom data type {TypeName}", customType.Name);
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to register custom data type",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Translate browse paths to node IDs
        /// </summary>
        public async Task<OpcUaOperationResult<List<NodeId>>> TranslateBrowsePathsAsync(List<BrowsePath> browsePaths)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<List<NodeId>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var response = await _session.TranslateBrowsePathsToNodeIdsAsync(null, new BrowsePathCollection(browsePaths));

                var nodeIds = new List<NodeId>();

                if (response?.Results != null)
                {
                    foreach (var result in response.Results)
                    {
                        if (StatusCode.IsGood(result.StatusCode) && result.Targets?.Count > 0)
                        {
                            nodeIds.Add(ExpandedNodeId.ToNodeId(result.Targets[0].TargetId, _session.NamespaceUris));
                        }
                        else
                        {
                            nodeIds.Add(NodeId.Null);
                        }
                    }
                }

                return new OpcUaOperationResult<List<NodeId>>
                {
                    IsSuccess = true,
                    Message = $"Translated {browsePaths.Count} browse paths",
                    Data = nodeIds
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error translating browse paths");
                
                return new OpcUaOperationResult<List<NodeId>>
                {
                    IsSuccess = false,
                    Message = "Failed to translate browse paths",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Find servers on network
        /// </summary>
        public async Task<OpcUaOperationResult<List<ApplicationDescription>>> FindServersAsync(string? discoveryUrl = null)
        {
            try
            {
                var endpointConfiguration = EndpointConfiguration.Create(_applicationConfiguration);
                var url = discoveryUrl ?? _config.EndpointUrl;
                
                var servers = await DiscoveryClient.FindServersAsync(url, endpointConfiguration);

                return new OpcUaOperationResult<List<ApplicationDescription>>
                {
                    IsSuccess = true,
                    Message = $"Found {servers.Count} servers",
                    Data = servers.ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding servers");
                
                return new OpcUaOperationResult<List<ApplicationDescription>>
                {
                    IsSuccess = false,
                    Message = "Failed to find servers",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Find servers on network with discovery
        /// </summary>
        public async Task<OpcUaOperationResult<List<ServerOnNetwork>>> FindServersOnNetworkAsync(string? discoveryUrl = null)
        {
            try
            {
                var endpointConfiguration = EndpointConfiguration.Create(_applicationConfiguration);
                var url = discoveryUrl ?? _config.EndpointUrl;
                
                var servers = await DiscoveryClient.FindServersOnNetworkAsync(url, endpointConfiguration);

                return new OpcUaOperationResult<List<ServerOnNetwork>>
                {
                    IsSuccess = true,
                    Message = $"Found {servers.Count} servers on network",
                    Data = servers.ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding servers on network");
                
                return new OpcUaOperationResult<List<ServerOnNetwork>>
                {
                    IsSuccess = false,
                    Message = "Failed to find servers on network",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }
    }
}
