using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Opc.Ua;
using SmartConnector.OpcUa.Client;
using Spectre.Console;

namespace SmartConnector.OpcUa.ConsoleUI
{
    class Program
    {
        private static IOpcUaClient? _opcUaClient;
        private static bool _isRunning = true;

        static async Task Main(string[] args)
        {
            // Setup console
            Console.Title = "SmartConnector OPC UA Console Client";
            
            // Create services
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<OpcUaClient>>();

            // Create OPC UA client
            var config = new OpcUaClientConfiguration
            {
                EndpointUrl = "opc.tcp://localhost:4840",
                ApplicationName = "SmartConnector Console Client",
                ApplicationUri = "urn:SmartConnector:ConsoleClient",
                Security = new OpcUaSecurityConfiguration
                {
                    SecurityPolicy = SecurityPolicies.None,
                    MessageSecurityMode = MessageSecurityMode.None,
                    UserIdentityType = UserTokenType.Anonymous
                }
            };

            _opcUaClient = new OpcUaClient(config, logger);

            // Setup event handlers
            _opcUaClient.ConnectionStatusChanged += OnConnectionStatusChanged;
            _opcUaClient.DataChanged += OnDataChanged;
            _opcUaClient.EventReceived += OnEventReceived;

            // Show welcome screen
            ShowWelcomeScreen();

            // Main menu loop
            while (_isRunning)
            {
                try
                {
                    await ShowMainMenu();
                }
                catch (Exception ex)
                {
                    AnsiConsole.WriteException(ex);
                    AnsiConsole.WriteLine();
                    AnsiConsole.MarkupLine("[red]Press any key to continue...[/]");
                    Console.ReadKey();
                }
            }

            // Cleanup
            if (_opcUaClient?.IsConnected == true)
            {
                await _opcUaClient.DisconnectAsync();
            }
            _opcUaClient?.Dispose();

            AnsiConsole.MarkupLine("[green]Goodbye![/]");
        }

        static void ShowWelcomeScreen()
        {
            AnsiConsole.Clear();
            
            var rule = new Rule("[bold blue]SmartConnector OPC UA Console Client[/]");
            rule.Alignment = Justify.Center;
            AnsiConsole.Write(rule);
            
            AnsiConsole.WriteLine();
            
            var panel = new Panel(new Markup(
                "[bold]Welcome to SmartConnector OPC UA Console Client![/]\n\n" +
                "This interactive console application allows you to:\n" +
                "• Connect to OPC UA servers\n" +
                "• Browse the server address space\n" +
                "• Read and write node values\n" +
                "• Monitor real-time data changes\n" +
                "• Discover servers on the network\n\n" +
                "[dim]Use the arrow keys to navigate menus and press Enter to select.[/]"))
            {
                Header = new PanelHeader("Getting Started"),
                Border = BoxBorder.Rounded
            };
            
            AnsiConsole.Write(panel);
            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task ShowMainMenu()
        {
            AnsiConsole.Clear();
            
            // Show connection status
            var statusColor = _opcUaClient?.IsConnected == true ? "green" : "red";
            var statusText = _opcUaClient?.IsConnected == true ? "Connected" : "Disconnected";
            AnsiConsole.MarkupLine($"[bold]Connection Status:[/] [{statusColor}]{statusText}[/]");
            
            if (_opcUaClient?.IsConnected == true)
            {
                AnsiConsole.MarkupLine($"[dim]Server: {(_opcUaClient as OpcUaClient)?.Session?.Endpoint?.EndpointUrl ?? "Unknown"}[/]");
            }
            
            AnsiConsole.WriteLine();

            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("What would you like to do?")
                    .PageSize(10)
                    .AddChoices(new[] {
                        "Connect to Server",
                        "Disconnect from Server",
                        "Browse Address Space",
                        "Read Node Values",
                        "Write Node Values",
                        "Monitor Data Changes",
                        "Server Discovery",
                        "Server Information",
                        "Settings",
                        "Exit"
                    }));

            switch (choice)
            {
                case "Connect to Server":
                    await ConnectToServer();
                    break;
                case "Disconnect from Server":
                    await DisconnectFromServer();
                    break;
                case "Browse Address Space":
                    await BrowseAddressSpace();
                    break;
                case "Read Node Values":
                    await ReadNodeValues();
                    break;
                case "Write Node Values":
                    await WriteNodeValues();
                    break;
                case "Monitor Data Changes":
                    await MonitorDataChanges();
                    break;
                case "Server Discovery":
                    await DiscoverServers();
                    break;
                case "Server Information":
                    await ShowServerInformation();
                    break;
                case "Settings":
                    await ShowSettings();
                    break;
                case "Exit":
                    _isRunning = false;
                    break;
            }
        }

        static async Task ConnectToServer()
        {
            if (_opcUaClient?.IsConnected == true)
            {
                AnsiConsole.MarkupLine("[yellow]Already connected to server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            var endpointUrl = AnsiConsole.Ask<string>("Enter OPC UA server URL:", "opc.tcp://localhost:4840");

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[yellow]Connecting to server...[/]");

            var result = await _opcUaClient!.ConnectAsync();
            
            if (result.IsSuccess)
            {
                AnsiConsole.MarkupLine("[green]✓ Connected successfully![/]");
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ Connection failed: {result.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task DisconnectFromServer()
        {
            if (_opcUaClient?.IsConnected != true)
            {
                AnsiConsole.MarkupLine("[yellow]Not connected to any server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            AnsiConsole.MarkupLine("[yellow]Disconnecting from server...[/]");

            var result = await _opcUaClient.DisconnectAsync();
            
            if (result.IsSuccess)
            {
                AnsiConsole.MarkupLine("[green]✓ Disconnected successfully![/]");
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ Disconnect failed: {result.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task BrowseAddressSpace()
        {
            if (_opcUaClient?.IsConnected != true)
            {
                AnsiConsole.MarkupLine("[red]Not connected to server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            AnsiConsole.MarkupLine("[yellow]Browsing address space...[/]");

            var result = await _opcUaClient.BrowseAsync();
            
            if (result.IsSuccess && result.Data != null)
            {
                var table = new Table();
                table.AddColumn("Display Name");
                table.AddColumn("Node Class");
                table.AddColumn("Node ID");

                foreach (var node in result.Data.Take(20)) // Limit to first 20 nodes
                {
                    var nodeClassColor = node.NodeClass switch
                    {
                        NodeClass.Object => "blue",
                        NodeClass.Variable => "green",
                        NodeClass.Method => "yellow",
                        _ => "white"
                    };

                    table.AddRow(
                        node.DisplayName,
                        $"[{nodeClassColor}]{node.NodeClass}[/]",
                        $"[dim]{node.NodeId}[/]"
                    );
                }

                AnsiConsole.Write(table);
                
                if (result.Data.Count > 20)
                {
                    AnsiConsole.MarkupLine($"[dim]... and {result.Data.Count - 20} more nodes[/]");
                }
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ Browse failed: {result.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task ReadNodeValues()
        {
            if (_opcUaClient?.IsConnected != true)
            {
                AnsiConsole.MarkupLine("[red]Not connected to server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            var nodeIdString = AnsiConsole.Ask<string>("Enter Node ID to read:", "i=2258");

            try
            {
                var nodeId = NodeId.Parse(nodeIdString);
                AnsiConsole.MarkupLine("[yellow]Reading node value...[/]");

                var result = await _opcUaClient.ReadAsync(nodeId);
                
                if (result.IsSuccess && result.Data != null)
                {
                    var table = new Table();
                    table.AddColumn("Property");
                    table.AddColumn("Value");

                    table.AddRow("Node ID", result.Data.NodeId.ToString());
                    table.AddRow("Value", result.Data.Value?.ToString() ?? "null");
                    table.AddRow("Status Code", result.Data.StatusCode.ToString());
                    table.AddRow("Source Timestamp", result.Data.SourceTimestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                    table.AddRow("Server Timestamp", result.Data.ServerTimestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"));

                    AnsiConsole.Write(table);
                }
                else
                {
                    AnsiConsole.MarkupLine($"[red]✗ Read failed: {result.Message}[/]");
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗ Invalid Node ID: {ex.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task WriteNodeValues()
        {
            if (_opcUaClient?.IsConnected != true)
            {
                AnsiConsole.MarkupLine("[red]Not connected to server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            var nodeIdString = AnsiConsole.Ask<string>("Enter Node ID to write:");
            var valueString = AnsiConsole.Ask<string>("Enter value to write:");

            try
            {
                var nodeId = NodeId.Parse(nodeIdString);
                AnsiConsole.MarkupLine("[yellow]Writing node value...[/]");

                // Try to parse value as different types
                object value = valueString;
                if (double.TryParse(valueString, out var doubleValue))
                    value = doubleValue;
                else if (bool.TryParse(valueString, out var boolValue))
                    value = boolValue;

                var result = await _opcUaClient.WriteAsync(nodeId, value);
                
                if (result.IsSuccess)
                {
                    AnsiConsole.MarkupLine("[green]✓ Value written successfully![/]");
                }
                else
                {
                    AnsiConsole.MarkupLine($"[red]✗ Write failed: {result.Message}[/]");
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗ Error: {ex.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task MonitorDataChanges()
        {
            if (_opcUaClient?.IsConnected != true)
            {
                AnsiConsole.MarkupLine("[red]Not connected to server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            var nodeIdString = AnsiConsole.Ask<string>("Enter Node ID to monitor:", "i=2258");

            try
            {
                var nodeId = NodeId.Parse(nodeIdString);
                
                // Create subscription
                var subscriptionSettings = new OpcUaSubscriptionSettings
                {
                    DisplayName = "Console Monitor",
                    PublishingInterval = 1000
                };

                var subscriptionResult = await _opcUaClient.CreateSubscriptionAsync(subscriptionSettings);
                if (!subscriptionResult.IsSuccess || !subscriptionResult.Data.HasValue)
                {
                    AnsiConsole.MarkupLine("[red]✗ Failed to create subscription[/]");
                    return;
                }

                var subscriptionId = subscriptionResult.Data.Value;

                // Add monitored item
                var monitoredItems = new List<OpcUaMonitoredItem>
                {
                    new OpcUaMonitoredItem
                    {
                        ClientHandle = 1,
                        NodeId = nodeId,
                        SamplingInterval = 1000
                    }
                };

                var addItemsResult = await _opcUaClient.AddMonitoredItemsAsync(subscriptionId, monitoredItems);
                if (!addItemsResult.IsSuccess)
                {
                    AnsiConsole.MarkupLine("[red]✗ Failed to add monitored item[/]");
                    return;
                }

                AnsiConsole.MarkupLine("[green]✓ Monitoring started![/]");
                AnsiConsole.MarkupLine("[yellow]Press any key to stop monitoring...[/]");
                AnsiConsole.WriteLine();

                // Wait for key press
                Console.ReadKey();

                // Clean up subscription
                await _opcUaClient.DeleteSubscriptionAsync(subscriptionId);
                AnsiConsole.MarkupLine("[yellow]Monitoring stopped.[/]");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗ Error: {ex.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task DiscoverServers()
        {
            var discoveryUrl = AnsiConsole.Ask<string>("Enter discovery URL:", "opc.tcp://localhost:4840");

            AnsiConsole.MarkupLine("[yellow]Discovering servers...[/]");

            var result = await _opcUaClient!.FindServersAsync(discoveryUrl);
            
            if (result.IsSuccess && result.Data != null)
            {
                var table = new Table();
                table.AddColumn("Application Name");
                table.AddColumn("Application URI");
                table.AddColumn("Type");

                foreach (var server in result.Data)
                {
                    table.AddRow(
                        server.ApplicationName?.Text ?? "Unknown",
                        server.ApplicationUri ?? "Unknown",
                        server.ApplicationType.ToString()
                    );
                }

                AnsiConsole.Write(table);
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ Discovery failed: {result.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task ShowServerInformation()
        {
            if (_opcUaClient?.IsConnected != true)
            {
                AnsiConsole.MarkupLine("[red]Not connected to server![/]");
                AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
                Console.ReadKey();
                return;
            }

            AnsiConsole.MarkupLine("[yellow]Getting server information...[/]");

            var result = await _opcUaClient.GetServerInfoAsync();
            
            if (result.IsSuccess && result.Data != null)
            {
                var info = result.Data;
                
                var table = new Table();
                table.AddColumn("Property");
                table.AddColumn("Value");

                table.AddRow("Application Name", info.ApplicationName);
                table.AddRow("Application URI", info.ApplicationUri);
                table.AddRow("Product URI", info.ProductUri);
                table.AddRow("Software Version", info.SoftwareVersion);
                table.AddRow("Build Number", info.BuildNumber);
                table.AddRow("Build Date", info.BuildDate.ToString("yyyy-MM-dd"));

                AnsiConsole.Write(table);
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ Failed to get server info: {result.Message}[/]");
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static async Task ShowSettings()
        {
            AnsiConsole.MarkupLine("[yellow]Settings functionality would be implemented here[/]");
            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[dim]Press any key to continue...[/]");
            Console.ReadKey();
        }

        static void OnConnectionStatusChanged(object? sender, OpcUaConnectionEventArgs e)
        {
            var status = e.IsConnected ? "Connected" : "Disconnected";
            var color = e.IsConnected ? "green" : "red";
            
            // Only show if we're not in a menu
            if (Console.CursorTop > 0)
            {
                AnsiConsole.MarkupLine($"[{color}]Connection status: {status}[/]");
            }
        }

        static void OnDataChanged(object? sender, OpcUaNotificationEventArgs e)
        {
            foreach (var change in e.DataChanges)
            {
                AnsiConsole.MarkupLine($"[cyan]Data changed: {change.NodeId} = {change.Value} ({change.StatusCode})[/]");
            }
        }

        static void OnEventReceived(object? sender, OpcUaEventNotificationArgs e)
        {
            AnsiConsole.MarkupLine($"[magenta]Event: {e.Message} (Severity: {e.Severity})[/]");
        }
    }
}
