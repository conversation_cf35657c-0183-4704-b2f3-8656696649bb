{"format": 1, "restore": {"G:\\ua\\client\\SmartConnector.Ews.Server\\SmartConnector.Ews.Server.csproj": {}}, "projects": {"G:\\ua\\client\\SmartConnector.Ews.Server\\SmartConnector.Ews.Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\ua\\client\\SmartConnector.Ews.Server\\SmartConnector.Ews.Server.csproj", "projectName": "SmartConnector.Ews.Server", "projectPath": "G:\\ua\\client\\SmartConnector.Ews.Server\\SmartConnector.Ews.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\ua\\client\\SmartConnector.Ews.Server\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Exchange.WebServices": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.10.3, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[4.10.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}