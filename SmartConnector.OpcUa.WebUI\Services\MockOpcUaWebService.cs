using SmartConnector.OpcUa.WebUI.Hubs;
using Microsoft.AspNetCore.SignalR;

namespace SmartConnector.OpcUa.WebUI.Services
{
    /// <summary>
    /// Mock OPC UA service for demonstration purposes
    /// </summary>
    public class MockOpcUaWebService
    {
        private readonly IHubContext<OpcUaHub> _hubContext;
        private readonly ILogger<MockOpcUaWebService> _logger;
        private bool _isConnected = false;
        private readonly Timer _dataUpdateTimer;

        public bool IsConnected => _isConnected;
        public string ServerUrl { get; private set; } = "opc.tcp://demo-server:4840";

        public MockOpcUaWebService(IHubContext<OpcUaHub> hubContext, ILogger<MockOpcUaWebService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;

            // Simulate periodic data updates
            _dataUpdateTimer = new Timer(SimulateDataUpdate, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(2));
        }

        public async Task StartAsync()
        {
            _logger.LogInformation("Mock OPC UA Web Service starting...");
            
            // Simulate connection after a delay
            await Task.Delay(2000);
            _isConnected = true;
            
            await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", true, "Connected to demo server");
            _logger.LogInformation("Mock service connected to demo server");
        }

        public async Task<MockOperationResult> ConnectAsync(string endpointUrl)
        {
            try
            {
                ServerUrl = endpointUrl;
                await Task.Delay(1000); // Simulate connection time
                
                _isConnected = true;
                await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", true, "Connected successfully");
                
                return new MockOperationResult { IsSuccess = true, Message = "Connected successfully" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock connect");
                return new MockOperationResult { IsSuccess = false, Message = ex.Message };
            }
        }

        public async Task<MockOperationResult> DisconnectAsync()
        {
            try
            {
                _isConnected = false;
                await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", false, "Disconnected");
                
                return new MockOperationResult { IsSuccess = true, Message = "Disconnected successfully" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock disconnect");
                return new MockOperationResult { IsSuccess = false, Message = ex.Message };
            }
        }

        public async Task<MockOperationResult<List<MockNode>>> BrowseAsync(string? nodeId = null)
        {
            try
            {
                if (!_isConnected)
                {
                    return new MockOperationResult<List<MockNode>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                // Return mock nodes
                var nodes = new List<MockNode>
                {
                    new MockNode { NodeId = "ns=0;i=85", DisplayName = "Objects", NodeClass = "Object", HasChildren = true },
                    new MockNode { NodeId = "ns=0;i=86", DisplayName = "Types", NodeClass = "Object", HasChildren = true },
                    new MockNode { NodeId = "ns=0;i=87", DisplayName = "Views", NodeClass = "Object", HasChildren = true },
                    new MockNode { NodeId = "ns=2;s=Temperature", DisplayName = "Temperature", NodeClass = "Variable", HasChildren = false },
                    new MockNode { NodeId = "ns=2;s=Pressure", DisplayName = "Pressure", NodeClass = "Variable", HasChildren = false },
                    new MockNode { NodeId = "ns=2;s=FlowRate", DisplayName = "Flow Rate", NodeClass = "Variable", HasChildren = false }
                };

                return new MockOperationResult<List<MockNode>>
                {
                    IsSuccess = true,
                    Message = $"Browsed {nodes.Count} nodes",
                    Data = nodes
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock browse");
                return new MockOperationResult<List<MockNode>>
                {
                    IsSuccess = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<MockOperationResult<List<MockValue>>> ReadAsync(List<string> nodeIds)
        {
            try
            {
                if (!_isConnected)
                {
                    return new MockOperationResult<List<MockValue>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var values = new List<MockValue>();
                var random = new Random();

                foreach (var nodeId in nodeIds)
                {
                    var value = nodeId switch
                    {
                        "ns=2;s=Temperature" => $"{20 + random.NextDouble() * 10:F2}°C",
                        "ns=2;s=Pressure" => $"{1000 + random.NextDouble() * 500:F1} Pa",
                        "ns=2;s=FlowRate" => $"{50 + random.NextDouble() * 20:F1} L/min",
                        "i=2258" => DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        "i=2256" => "Running",
                        _ => $"Mock value for {nodeId}"
                    };

                    values.Add(new MockValue
                    {
                        NodeId = nodeId,
                        Value = value,
                        StatusCode = "Good",
                        SourceTimestamp = DateTime.Now,
                        ServerTimestamp = DateTime.Now
                    });
                }

                return new MockOperationResult<List<MockValue>>
                {
                    IsSuccess = true,
                    Message = $"Read {values.Count} values",
                    Data = values
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock read");
                return new MockOperationResult<List<MockValue>>
                {
                    IsSuccess = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<MockOperationResult> WriteAsync(List<MockWriteValue> writeValues)
        {
            try
            {
                if (!_isConnected)
                {
                    return new MockOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                // Simulate write operation
                await Task.Delay(100);

                return new MockOperationResult
                {
                    IsSuccess = true,
                    Message = $"Successfully wrote {writeValues.Count} values"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock write");
                return new MockOperationResult { IsSuccess = false, Message = ex.Message };
            }
        }

        public async Task<MockOperationResult<uint>> CreateSubscriptionAsync(List<string> nodeIds, double publishingInterval)
        {
            try
            {
                if (!_isConnected)
                {
                    return new MockOperationResult<uint>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var subscriptionId = (uint)new Random().Next(1000, 9999);
                
                return new MockOperationResult<uint>
                {
                    IsSuccess = true,
                    Message = "Subscription created successfully",
                    Data = subscriptionId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock create subscription");
                return new MockOperationResult<uint> { IsSuccess = false, Message = ex.Message };
            }
        }

        public async Task<MockOperationResult> DeleteSubscriptionAsync(uint subscriptionId)
        {
            try
            {
                return new MockOperationResult
                {
                    IsSuccess = true,
                    Message = "Subscription deleted successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in mock delete subscription");
                return new MockOperationResult { IsSuccess = false, Message = ex.Message };
            }
        }

        public async Task<MockOperationResult<MockServerInfo>> GetServerInfoAsync()
        {
            try
            {
                var serverInfo = new MockServerInfo
                {
                    ApplicationName = "Demo OPC UA Server",
                    ApplicationUri = "urn:demo:opcua:server",
                    ProductUri = "http://demo.com/opcua",
                    SoftwareVersion = "1.0.0",
                    BuildNumber = "12345",
                    BuildDate = DateTime.Now.AddDays(-30),
                    ServerCapabilities = new List<string> { "DA", "HDA", "AC", "HD" },
                    SupportedProfiles = new List<string> { "Standard DataAccess Server", "Embedded DataAccess Server" }
                };

                return new MockOperationResult<MockServerInfo>
                {
                    IsSuccess = true,
                    Message = "Server information retrieved",
                    Data = serverInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mock server info");
                return new MockOperationResult<MockServerInfo> { IsSuccess = false, Message = ex.Message };
            }
        }

        public async Task<List<MockValue>?> GetRecentValuesAsync()
        {
            try
            {
                if (!_isConnected) return null;

                var random = new Random();
                return new List<MockValue>
                {
                    new MockValue
                    {
                        NodeId = "ns=2;s=Temperature",
                        DisplayName = "Temperature",
                        Value = $"{20 + random.NextDouble() * 10:F2}°C",
                        StatusCode = "Good",
                        SourceTimestamp = DateTime.Now
                    },
                    new MockValue
                    {
                        NodeId = "ns=2;s=Pressure",
                        DisplayName = "Pressure",
                        Value = $"{1000 + random.NextDouble() * 500:F1} Pa",
                        StatusCode = "Good",
                        SourceTimestamp = DateTime.Now
                    },
                    new MockValue
                    {
                        NodeId = "ns=2;s=FlowRate",
                        DisplayName = "Flow Rate",
                        Value = $"{50 + random.NextDouble() * 20:F1} L/min",
                        StatusCode = "Good",
                        SourceTimestamp = DateTime.Now
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent values");
                return null;
            }
        }

        public async Task<MockOperationResult<List<MockServer>>> FindServersAsync(string? discoveryUrl = null)
        {
            try
            {
                var servers = new List<MockServer>
                {
                    new MockServer
                    {
                        ApplicationName = "Demo OPC UA Server",
                        ApplicationUri = "urn:demo:opcua:server",
                        ApplicationType = "Server",
                        DiscoveryUrls = new[] { "opc.tcp://demo-server:4840" }
                    },
                    new MockServer
                    {
                        ApplicationName = "Test OPC UA Server",
                        ApplicationUri = "urn:test:opcua:server",
                        ApplicationType = "Server",
                        DiscoveryUrls = new[] { "opc.tcp://test-server:4840" }
                    }
                };

                return new MockOperationResult<List<MockServer>>
                {
                    IsSuccess = true,
                    Message = $"Found {servers.Count} servers",
                    Data = servers
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding servers");
                return new MockOperationResult<List<MockServer>> { IsSuccess = false, Message = ex.Message };
            }
        }

        private async void SimulateDataUpdate(object? state)
        {
            if (!_isConnected) return;

            try
            {
                var random = new Random();
                var dataChanges = new[]
                {
                    new
                    {
                        NodeId = "ns=2;s=Temperature",
                        Value = $"{20 + random.NextDouble() * 10:F2}°C",
                        StatusCode = "Good",
                        SourceTimestamp = DateTime.Now,
                        ServerTimestamp = DateTime.Now,
                        DisplayName = "Temperature"
                    },
                    new
                    {
                        NodeId = "ns=2;s=Pressure",
                        Value = $"{1000 + random.NextDouble() * 500:F1} Pa",
                        StatusCode = "Good",
                        SourceTimestamp = DateTime.Now,
                        ServerTimestamp = DateTime.Now,
                        DisplayName = "Pressure"
                    }
                };

                await _hubContext.Clients.All.SendAsync("DataChanged", dataChanges);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in simulated data update");
            }
        }
    }

    // Mock data models
    public class MockOperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class MockOperationResult<T> : MockOperationResult
    {
        public T? Data { get; set; }
    }

    public class MockNode
    {
        public string NodeId { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string NodeClass { get; set; } = string.Empty;
        public string BrowseName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool HasChildren { get; set; }
    }

    public class MockValue
    {
        public string NodeId { get; set; } = string.Empty;
        public string? DisplayName { get; set; }
        public string? Value { get; set; }
        public string StatusCode { get; set; } = string.Empty;
        public DateTime SourceTimestamp { get; set; }
        public DateTime ServerTimestamp { get; set; }
    }

    public class MockWriteValue
    {
        public string NodeId { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    public class MockServerInfo
    {
        public string ApplicationName { get; set; } = string.Empty;
        public string ApplicationUri { get; set; } = string.Empty;
        public string ProductUri { get; set; } = string.Empty;
        public string SoftwareVersion { get; set; } = string.Empty;
        public string BuildNumber { get; set; } = string.Empty;
        public DateTime BuildDate { get; set; }
        public List<string>? ServerCapabilities { get; set; }
        public List<string>? SupportedProfiles { get; set; }
    }

    public class MockServer
    {
        public string ApplicationName { get; set; } = string.Empty;
        public string ApplicationUri { get; set; } = string.Empty;
        public string ApplicationType { get; set; } = string.Empty;
        public string[]? DiscoveryUrls { get; set; }
    }
}
