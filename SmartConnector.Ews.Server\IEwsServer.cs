using Microsoft.Exchange.WebServices.Data;

namespace SmartConnector.Ews.Server
{
    /// <summary>
    /// Interface for EWS Server operations
    /// </summary>
    public interface IEwsServer
    {
        /// <summary>
        /// Test connection to Exchange server
        /// </summary>
        Task<EwsConnectionResult> TestConnectionAsync();

        /// <summary>
        /// Send email message
        /// </summary>
        Task<EwsOperationResult> SendEmailAsync(EwsEmailMessage message);

        /// <summary>
        /// Create calendar appointment
        /// </summary>
        Task<EwsOperationResult> CreateAppointmentAsync(EwsAppointment appointment);

        /// <summary>
        /// Create task
        /// </summary>
        Task<EwsOperationResult> CreateTaskAsync(EwsTask task);

        /// <summary>
        /// Get inbox messages
        /// </summary>
        Task<EwsOperationResult<List<EwsEmailMessage>>> GetInboxMessagesAsync(int maxItems = 10);

        /// <summary>
        /// Get calendar events
        /// </summary>
        Task<EwsOperationResult<List<EwsAppointment>>> GetCalendarEventsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Subscribe to mailbox changes
        /// </summary>
        Task<EwsOperationResult> SubscribeToMailboxChangesAsync(Action<EwsNotification> onNotification);

        /// <summary>
        /// Get server information
        /// </summary>
        Task<EwsOperationResult<EwsServerInfo>> GetServerInfoAsync();
    }

    /// <summary>
    /// EWS connection result
    /// </summary>
    public class EwsConnectionResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? ErrorDetails { get; set; }
        public EwsServerInfo? ServerInfo { get; set; }
    }

    /// <summary>
    /// EWS operation result
    /// </summary>
    public class EwsOperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? ErrorDetails { get; set; }
        public string? OperationId { get; set; }
    }

    /// <summary>
    /// Generic EWS operation result with data
    /// </summary>
    public class EwsOperationResult<T> : EwsOperationResult
    {
        public T? Data { get; set; }
    }

    /// <summary>
    /// EWS server information
    /// </summary>
    public class EwsServerInfo
    {
        public string ServerVersion { get; set; } = string.Empty;
        public string ExchangeVersion { get; set; } = string.Empty;
        public string TimeZone { get; set; } = string.Empty;
        public bool IsConnected { get; set; }
        public DateTime LastConnectionTime { get; set; }
    }

    /// <summary>
    /// EWS notification data
    /// </summary>
    public class EwsNotification
    {
        public string NotificationType { get; set; } = string.Empty;
        public string FolderId { get; set; } = string.Empty;
        public string ItemId { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }
}
