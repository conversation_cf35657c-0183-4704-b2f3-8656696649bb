<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>SmartConnector.Ews.Server</PackageId>
    <Version>1.0.0</Version>
    <Authors>SmartConnector</Authors>
    <Description>EWS Server library for SmartConnector integration with Kepware and Exchange Web Services</Description>
    <Copyright>Copyright © 2025</Copyright>
    <PackageTags>EWS;Exchange;Kepware;SmartConnector;Industrial;Automation</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Exchange.WebServices" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.10.3" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="4.10.3" />
  </ItemGroup>

</Project>
