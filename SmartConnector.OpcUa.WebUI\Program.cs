using SmartConnector.OpcUa.Client;
using SmartConnector.OpcUa.Client.Extensions;
using SmartConnector.OpcUa.Client.Services;
using SmartConnector.OpcUa.WebUI.Hubs;
using SmartConnector.OpcUa.WebUI.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();

// Register Real OPC UA Service for connecting to actual servers
builder.Services.AddSingleton<RealOpcUaService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapHub<OpcUaHub>("/opcuahub");

// Start real OPC UA service
var realOpcUaService = app.Services.GetRequiredService<RealOpcUaService>();
await realOpcUaService.StartAsync();

app.Run();

// Ensure to flush and stop internal timers/threads before application-exit
Log.CloseAndFlush();
