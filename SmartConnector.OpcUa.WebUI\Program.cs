using SmartConnector.OpcUa.Client;
using SmartConnector.OpcUa.Client.Extensions;
using SmartConnector.OpcUa.Client.Services;
using SmartConnector.OpcUa.WebUI.Hubs;
using SmartConnector.OpcUa.WebUI.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();

// Configure OPC UA Client
var opcUaConfig = new OpcUaClientConfiguration
{
    EndpointUrl = builder.Configuration.GetValue<string>("OpcUa:EndpointUrl") ?? "opc.tcp://localhost:4840",
    ApplicationName = "SmartConnector OPC UA Web UI",
    ApplicationUri = "urn:SmartConnector:OpcUaWebUI",
    Security = new OpcUaSecurityConfiguration
    {
        SecurityPolicy = Opc.Ua.SecurityPolicies.None,
        MessageSecurityMode = Opc.Ua.MessageSecurityMode.None,
        UserIdentityType = Opc.Ua.UserTokenType.Anonymous
    },
    Connection = new OpcUaConnectionConfiguration
    {
        AutoReconnect = true,
        ReconnectPeriod = 10000
    }
};

var serviceConfig = new OpcUaClientServiceConfiguration
{
    HealthCheckInterval = 30000,
    AutoReconnect = true
};

// Add OPC UA services
builder.Services.AddOpcUaClient(opcUaConfig, serviceConfig);

// Add custom services
builder.Services.AddSingleton<OpcUaWebService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapHub<OpcUaHub>("/opcuahub");

// Start OPC UA web service
var opcUaWebService = app.Services.GetRequiredService<OpcUaWebService>();
await opcUaWebService.StartAsync();

app.Run();

// Ensure to flush and stop internal timers/threads before application-exit
Log.CloseAndFlush();
