using Microsoft.AspNetCore.Mvc;
using SmartConnector.OpcUa.WebUI.Services;

namespace SmartConnector.OpcUa.WebUI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OpcUaController : ControllerBase
    {
        private readonly MockOpcUaWebService _mockOpcUaWebService;
        private readonly ILogger<OpcUaController> _logger;

        public OpcUaController(MockOpcUaWebService mockOpcUaWebService, ILogger<OpcUaController> logger)
        {
            _mockOpcUaWebService = mockOpcUaWebService;
            _logger = logger;
        }

        [HttpGet("status")]
        public IActionResult GetStatus()
        {
            return Ok(new
            {
                IsConnected = _mockOpcUaWebService.IsConnected,
                ServerUrl = _mockOpcUaWebService.ServerUrl,
                LastUpdate = DateTime.Now
            });
        }

        [HttpPost("connect")]
        public async Task<IActionResult> Connect([FromBody] ConnectRequest request)
        {
            try
            {
                var result = await _mockOpcUaWebService.ConnectAsync(request.EndpointUrl);
                return Ok(new { Success = result.IsSuccess, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to OPC UA server");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("disconnect")]
        public async Task<IActionResult> Disconnect()
        {
            try
            {
                var result = await _mockOpcUaWebService.DisconnectAsync();
                return Ok(new { Success = result.IsSuccess, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from OPC UA server");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("browse")]
        public async Task<IActionResult> Browse([FromQuery] string? nodeId = null)
        {
            try
            {
                if (!_mockOpcUaWebService.IsConnected)
                {
                    return BadRequest(new { Success = false, Message = "Not connected to server" });
                }

                var result = await _mockOpcUaWebService.BrowseAsync(nodeId);
                
                if (result.IsSuccess && result.Data != null)
                {
                    var nodes = result.Data.Select(n => new
                    {
                        NodeId = n.NodeId,
                        DisplayName = n.DisplayName,
                        NodeClass = n.NodeClass,
                        BrowseName = n.BrowseName,
                        Description = n.Description,
                        HasChildren = n.HasChildren
                    });

                    return Ok(new { Success = true, Data = nodes });
                }

                return BadRequest(new { Success = false, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error browsing nodes");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("read")]
        public async Task<IActionResult> ReadValues([FromBody] ReadRequest request)
        {
            try
            {
                if (!_mockOpcUaWebService.IsConnected)
                {
                    return BadRequest(new { Success = false, Message = "Not connected to server" });
                }

                var result = await _mockOpcUaWebService.ReadAsync(request.NodeIds);

                if (result.IsSuccess && result.Data != null)
                {
                    var values = result.Data.Select(v => new
                    {
                        NodeId = v.NodeId,
                        Value = v.Value?.ToString(),
                        StatusCode = v.StatusCode,
                        SourceTimestamp = v.SourceTimestamp,
                        ServerTimestamp = v.ServerTimestamp
                    });

                    return Ok(new { Success = true, Data = values });
                }

                return BadRequest(new { Success = false, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading values");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("write")]
        public async Task<IActionResult> WriteValues([FromBody] WriteRequest request)
        {
            try
            {
                if (!_mockOpcUaWebService.IsConnected)
                {
                    return BadRequest(new { Success = false, Message = "Not connected to server" });
                }

                var writeValues = request.WriteValues.Select(wv => new MockWriteValue
                {
                    NodeId = wv.NodeId,
                    Value = wv.Value
                }).ToList();

                var result = await _mockOpcUaWebService.WriteAsync(writeValues);
                return Ok(new { Success = result.IsSuccess, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing values");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("subscribe")]
        public async Task<IActionResult> CreateSubscription([FromBody] SubscriptionRequest request)
        {
            try
            {
                if (!_mockOpcUaWebService.IsConnected)
                {
                    return BadRequest(new { Success = false, Message = "Not connected to server" });
                }

                var subscriptionId = await _mockOpcUaWebService.CreateSubscriptionAsync(request.NodeIds, request.PublishingInterval);

                return Ok(new { Success = true, SubscriptionId = subscriptionId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete("subscribe/{subscriptionId}")]
        public async Task<IActionResult> DeleteSubscription(uint subscriptionId)
        {
            try
            {
                var result = await _mockOpcUaWebService.DeleteSubscriptionAsync(subscriptionId);
                return Ok(new { Success = result.IsSuccess, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subscription");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("servers")]
        public async Task<IActionResult> FindServers([FromQuery] string? discoveryUrl = null)
        {
            try
            {
                var result = await _mockOpcUaWebService.FindServersAsync(discoveryUrl);
                
                if (result.IsSuccess && result.Data != null)
                {
                    var servers = result.Data.Select(s => new
                    {
                        ApplicationName = s.ApplicationName,
                        ApplicationUri = s.ApplicationUri,
                        ApplicationType = s.ApplicationType,
                        DiscoveryUrls = s.DiscoveryUrls
                    });

                    return Ok(new { Success = true, Data = servers });
                }

                return BadRequest(new { Success = false, Message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding servers");
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }
    }

    // Request/Response models
    public class ConnectRequest
    {
        public string EndpointUrl { get; set; } = string.Empty;
    }

    public class ReadRequest
    {
        public List<string> NodeIds { get; set; } = new();
    }

    public class WriteRequest
    {
        public List<WriteValueRequest> WriteValues { get; set; } = new();
    }

    public class WriteValueRequest
    {
        public string NodeId { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    public class SubscriptionRequest
    {
        public List<string> NodeIds { get; set; } = new();
        public double PublishingInterval { get; set; } = 1000;
    }
}
