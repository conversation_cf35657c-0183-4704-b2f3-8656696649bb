using Microsoft.AspNetCore.SignalR;
using SmartConnector.Kepware.WebUI.Services;
using SmartConnector.Kepware.WebUI.Models;

namespace SmartConnector.Kepware.WebUI.Hubs
{
    /// <summary>
    /// SignalR Hub for real-time Kepware data communication
    /// </summary>
    public class KepwareHub : Hub
    {
        private readonly KepwareDataService _kepwareDataService;
        private readonly ILogger<KepwareHub> _logger;

        public KepwareHub(KepwareDataService kepwareDataService, ILogger<KepwareHub> logger)
        {
            _kepwareDataService = kepwareDataService;
            _logger = logger;
        }

        /// <summary>
        /// Client connects to hub
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);
            
            // Send current connection status
            await Clients.Caller.SendAsync("ConnectionStatusChanged", new
            {
                IsConnected = _kepwareDataService.IsConnected,
                Message = _kepwareDataService.IsConnected ? "Connected to Kepware" : "Not connected to Kepware",
                Timestamp = DateTime.UtcNow
            });

            await base.OnConnectedAsync();
        }

        /// <summary>
        /// Client disconnects from hub
        /// </summary>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// Read tags from Kepware
        /// </summary>
        public async Task ReadTags(List<string> tagIds)
        {
            try
            {
                _logger.LogDebug("Reading {Count} tags for client {ConnectionId}", tagIds.Count, Context.ConnectionId);
                
                var result = await _kepwareDataService.ReadTagsAsync(tagIds);
                
                await Clients.Caller.SendAsync("TagReadResult", new
                {
                    IsSuccess = result.IsSuccess,
                    Message = result.Message,
                    Data = result.Data,
                    ErrorDetails = result.ErrorDetails,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading tags for client {ConnectionId}", Context.ConnectionId);
                
                await Clients.Caller.SendAsync("TagReadResult", new
                {
                    IsSuccess = false,
                    Message = "Error reading tags",
                    ErrorDetails = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Write tags to Kepware
        /// </summary>
        public async Task WriteTags(List<KepwareTagWrite> tagWrites)
        {
            try
            {
                _logger.LogDebug("Writing {Count} tags for client {ConnectionId}", tagWrites.Count, Context.ConnectionId);
                
                var result = await _kepwareDataService.WriteTagsAsync(tagWrites);
                
                await Clients.Caller.SendAsync("TagWriteResult", new
                {
                    IsSuccess = result.IsSuccess,
                    Message = result.Message,
                    ErrorDetails = result.ErrorDetails,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing tags for client {ConnectionId}", Context.ConnectionId);
                
                await Clients.Caller.SendAsync("TagWriteResult", new
                {
                    IsSuccess = false,
                    Message = "Error writing tags",
                    ErrorDetails = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Subscribe to tag updates
        /// </summary>
        public async Task SubscribeToTags(List<string> tagIds)
        {
            try
            {
                _logger.LogDebug("Subscribing to {Count} tags for client {ConnectionId}", tagIds.Count, Context.ConnectionId);
                
                var success = await _kepwareDataService.SubscribeToTagsAsync(tagIds);
                
                await Clients.Caller.SendAsync("SubscriptionResult", new
                {
                    IsSuccess = success,
                    Message = success ? "Successfully subscribed to tags" : "Failed to subscribe to tags",
                    TagIds = tagIds,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing to tags for client {ConnectionId}", Context.ConnectionId);
                
                await Clients.Caller.SendAsync("SubscriptionResult", new
                {
                    IsSuccess = false,
                    Message = "Error subscribing to tags",
                    ErrorDetails = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Unsubscribe from tag updates
        /// </summary>
        public async Task UnsubscribeFromTags(List<string> tagIds)
        {
            try
            {
                _logger.LogDebug("Unsubscribing from {Count} tags for client {ConnectionId}", tagIds.Count, Context.ConnectionId);
                
                var success = await _kepwareDataService.UnsubscribeFromTagsAsync(tagIds);
                
                await Clients.Caller.SendAsync("UnsubscriptionResult", new
                {
                    IsSuccess = success,
                    Message = success ? "Successfully unsubscribed from tags" : "Failed to unsubscribe from tags",
                    TagIds = tagIds,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unsubscribing from tags for client {ConnectionId}", Context.ConnectionId);
                
                await Clients.Caller.SendAsync("UnsubscriptionResult", new
                {
                    IsSuccess = false,
                    Message = "Error unsubscribing from tags",
                    ErrorDetails = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Get server status
        /// </summary>
        public async Task GetServerStatus()
        {
            try
            {
                _logger.LogDebug("Getting server status for client {ConnectionId}", Context.ConnectionId);
                
                var result = await _kepwareDataService.GetServerStatusAsync();
                
                await Clients.Caller.SendAsync("ServerStatusResult", new
                {
                    IsSuccess = result.IsSuccess,
                    Message = result.Message,
                    Data = result.Data,
                    ErrorDetails = result.ErrorDetails,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server status for client {ConnectionId}", Context.ConnectionId);
                
                await Clients.Caller.SendAsync("ServerStatusResult", new
                {
                    IsSuccess = false,
                    Message = "Error getting server status",
                    ErrorDetails = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Get subscribed tags
        /// </summary>
        public async Task GetSubscribedTags()
        {
            try
            {
                var subscribedTags = _kepwareDataService.GetSubscribedTags();
                
                await Clients.Caller.SendAsync("SubscribedTagsResult", new
                {
                    IsSuccess = true,
                    Data = subscribedTags,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscribed tags for client {ConnectionId}", Context.ConnectionId);
                
                await Clients.Caller.SendAsync("SubscribedTagsResult", new
                {
                    IsSuccess = false,
                    Message = "Error getting subscribed tags",
                    ErrorDetails = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }
    }
}
