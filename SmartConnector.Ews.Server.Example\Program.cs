using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmartConnector.Ews.Server;
using SmartConnector.Ews.Server.Extensions;
using SmartConnector.Ews.Server.Services;

namespace SmartConnector.Ews.Server.Example
{
    /// <summary>
    /// Example program demonstrating SmartConnector EWS Server usage
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("SmartConnector EWS Server Example");
            Console.WriteLine("==================================");

            // Create host builder
            var hostBuilder = Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // Configure logging
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });

                    // Configure EWS
                    var ewsConfig = new EwsConfiguration
                    {
                        ExchangeUrl = "https://outlook.office365.com/EWS/Exchange.asmx",
                        Username = "<EMAIL>",
                        Password = "your-password",
                        ExchangeVersion = Microsoft.Exchange.WebServices.Data.ExchangeVersion.Exchange2016,
                        TimeoutMinutes = 5,
                        EnableTracing = true,
                        TraceFilePath = "logs/ews-trace.log"
                    };

                    // Configure Kepware
                    var kepwareConfig = new KepwareConfiguration
                    {
                        Host = "localhost",
                        Port = 57412,
                        Username = "Administrator",
                        Password = "your-kepware-password",
                        UseHttps = false,
                        ApiVersion = "v1",
                        TimeoutMs = 30000
                    };

                    // Configure SmartConnector
                    var smartConnectorConfig = new SmartConnectorConfiguration
                    {
                        PollingIntervalMs = 5000,
                        MaxRetries = 3,
                        TimeoutMs = 30000,
                        EnableLogging = true,
                        LogLevel = "Information"
                    };

                    // Add SmartConnector services
                    services.AddSmartConnectorEws(ewsConfig, kepwareConfig, smartConnectorConfig);
                });

            // Build and run host
            var host = hostBuilder.Build();

            // Example of using the services directly
            await DemonstrateDirectUsage(host.Services);

            // Run the hosted service
            Console.WriteLine("Starting SmartConnector service...");
            Console.WriteLine("Press Ctrl+C to stop");
            
            await host.RunAsync();
        }

        /// <summary>
        /// Demonstrate direct usage of EWS and Kepware services
        /// </summary>
        static async Task DemonstrateDirectUsage(IServiceProvider services)
        {
            var logger = services.GetRequiredService<ILogger<Program>>();
            var ewsServer = services.GetRequiredService<IEwsServer>();
            var kepwareConnector = services.GetRequiredService<KepwareConnector>();

            try
            {
                logger.LogInformation("Testing connections...");

                // Test EWS connection
                var ewsTest = await ewsServer.TestConnectionAsync();
                logger.LogInformation("EWS Connection: {Status} - {Message}", 
                    ewsTest.IsSuccess ? "SUCCESS" : "FAILED", ewsTest.Message);

                // Test Kepware connection
                var kepwareTest = await kepwareConnector.TestConnectionAsync();
                logger.LogInformation("Kepware Connection: {Status} - {Message}", 
                    kepwareTest.IsSuccess ? "SUCCESS" : "FAILED", kepwareTest.Message);

                if (ewsTest.IsSuccess && kepwareTest.IsSuccess)
                {
                    logger.LogInformation("All connections successful! SmartConnector is ready.");

                    // Example: Send a test email
                    var emailResult = await ewsServer.SendEmailAsync(new EwsEmailMessage
                    {
                        To = new List<string> { "<EMAIL>" },
                        Subject = "SmartConnector Test Email",
                        Body = "This is a test email from SmartConnector EWS Server.",
                        Priority = EwsImportance.Normal
                    });

                    logger.LogInformation("Test email: {Status} - {Message}", 
                        emailResult.IsSuccess ? "SENT" : "FAILED", emailResult.Message);

                    // Example: Read some tags from Kepware
                    var tagResult = await kepwareConnector.ReadTagsAsync(new List<string> 
                    { 
                        "Channel1.Device1.Tag1", 
                        "Channel1.Device1.Tag2" 
                    });

                    if (tagResult.IsSuccess && tagResult.Data != null)
                    {
                        logger.LogInformation("Read {Count} tags from Kepware", tagResult.Data.Count);
                        foreach (var tag in tagResult.Data)
                        {
                            logger.LogInformation("Tag: {TagId} = {Value} ({Quality})", 
                                tag.TagId, tag.Value, tag.Quality);
                        }
                    }
                }
                else
                {
                    logger.LogError("Connection tests failed. Please check your configuration.");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during demonstration");
            }
        }
    }
}
