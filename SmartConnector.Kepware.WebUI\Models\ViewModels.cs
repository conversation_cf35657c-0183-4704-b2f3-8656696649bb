using SmartConnector.Kepware.WebUI.Models;

namespace SmartConnector.Kepware.WebUI.Models
{
    /// <summary>
    /// Dashboard view model
    /// </summary>
    public class DashboardViewModel
    {
        public bool IsConnected { get; set; }
        public string ConnectionStatus { get; set; } = string.Empty;
        public DateTime LastUpdateTime { get; set; }
        public int SubscribedTagsCount { get; set; }
        public KepwareServerStatus? ServerStatus { get; set; }
        public List<KepwareTagValue>? RecentTagValues { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Tag browser view model
    /// </summary>
    public class TagBrowserViewModel
    {
        public bool IsConnected { get; set; }
        public List<string> SubscribedTags { get; set; } = new();
        public List<KepwareTagValue>? TagValues { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Monitor view model
    /// </summary>
    public class MonitorViewModel
    {
        public bool IsConnected { get; set; }
        public List<string> SubscribedTags { get; set; } = new();
        public int PollingInterval { get; set; }
        public List<KepwareTagValue>? LiveTagValues { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Settings view model
    /// </summary>
    public class SettingsViewModel
    {
        public bool IsConnected { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public int PollingInterval { get; set; }
        public string? ErrorMessage { get; set; }
        public string? SuccessMessage { get; set; }
    }

    /// <summary>
    /// Tag operation view model
    /// </summary>
    public class TagOperationViewModel
    {
        public string TagId { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Operation { get; set; } = string.Empty; // Read, Write, Subscribe, Unsubscribe
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Connection status view model
    /// </summary>
    public class ConnectionStatusViewModel
    {
        public bool IsConnected { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Message { get; set; }
        public DateTime LastUpdate { get; set; }
        public KepwareServerStatus? ServerInfo { get; set; }
    }
}
