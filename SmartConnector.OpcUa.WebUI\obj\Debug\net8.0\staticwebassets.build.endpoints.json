{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.84t6txaswu.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3652"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:52:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "84t6txaswu"}, {"Name": "integrity", "Value": "sha256-GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3652"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:52:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:53:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A="}]}, {"Route": "js/site.pf8ytgao1u.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:53:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pf8ytgao1u"}, {"Name": "integrity", "Value": "sha256-NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "145"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.yyoirve2l3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "145"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yyoirve2l3"}, {"Name": "integrity", "Value": "sha256-Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.ijwclhwcxh.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ijwclhwcxh"}, {"Name": "integrity", "Value": "sha256-QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw="}]}, {"Route": "lib/jquery/dist/jquery.min.23ehigdbv1.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "107"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "23ehigdbv1"}, {"Name": "integrity", "Value": "sha256-cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "107"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ="}]}]}