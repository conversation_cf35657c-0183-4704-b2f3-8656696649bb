using Opc.Ua;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// Interface for OPC UA client operations
    /// </summary>
    public interface IOpcUaClient : IDisposable
    {
        /// <summary>
        /// Connection status
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// Current session
        /// </summary>
        Session? Session { get; }

        /// <summary>
        /// Connection events
        /// </summary>
        event EventHandler<OpcUaConnectionEventArgs>? ConnectionStatusChanged;
        event EventHandler<OpcUaNotificationEventArgs>? DataChanged;
        event EventHandler<OpcUaEventNotificationArgs>? EventReceived;
        event EventHandler<OpcUaErrorEventArgs>? ErrorOccurred;

        /// <summary>
        /// Connect to OPC UA server
        /// </summary>
        Task<OpcUaOperationResult> ConnectAsync();

        /// <summary>
        /// Disconnect from OPC UA server
        /// </summary>
        Task<OpcUaOperationResult> DisconnectAsync();

        /// <summary>
        /// Reconnect to OPC UA server
        /// </summary>
        Task<OpcUaOperationResult> ReconnectAsync();

        /// <summary>
        /// Browse server nodes
        /// </summary>
        Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseAsync(NodeId? startingNode = null, int maxReferencesToReturn = 1000);

        /// <summary>
        /// Browse server nodes recursively
        /// </summary>
        Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseRecursiveAsync(NodeId? startingNode = null, int maxDepth = 5);

        /// <summary>
        /// Read node values
        /// </summary>
        Task<OpcUaOperationResult<List<OpcUaValue>>> ReadAsync(List<NodeId> nodeIds);

        /// <summary>
        /// Read single node value
        /// </summary>
        Task<OpcUaOperationResult<OpcUaValue>> ReadAsync(NodeId nodeId);

        /// <summary>
        /// Write node values
        /// </summary>
        Task<OpcUaOperationResult> WriteAsync(List<OpcUaWriteValue> writeValues);

        /// <summary>
        /// Write single node value
        /// </summary>
        Task<OpcUaOperationResult> WriteAsync(NodeId nodeId, object value);

        /// <summary>
        /// Call method on server
        /// </summary>
        Task<OpcUaOperationResult<object[]>> CallMethodAsync(NodeId objectId, NodeId methodId, params object[] inputArguments);

        /// <summary>
        /// Create subscription for data monitoring
        /// </summary>
        Task<OpcUaOperationResult<uint>> CreateSubscriptionAsync(OpcUaSubscriptionSettings settings);

        /// <summary>
        /// Add monitored items to subscription
        /// </summary>
        Task<OpcUaOperationResult> AddMonitoredItemsAsync(uint subscriptionId, List<OpcUaMonitoredItem> monitoredItems);

        /// <summary>
        /// Remove monitored items from subscription
        /// </summary>
        Task<OpcUaOperationResult> RemoveMonitoredItemsAsync(uint subscriptionId, List<uint> monitoredItemIds);

        /// <summary>
        /// Delete subscription
        /// </summary>
        Task<OpcUaOperationResult> DeleteSubscriptionAsync(uint subscriptionId);

        /// <summary>
        /// Get server endpoints
        /// </summary>
        Task<OpcUaOperationResult<List<EndpointDescription>>> GetEndpointsAsync();

        /// <summary>
        /// Get server status
        /// </summary>
        Task<OpcUaOperationResult<OpcUaServerStatus>> GetServerStatusAsync();

        /// <summary>
        /// Get server information
        /// </summary>
        Task<OpcUaOperationResult<OpcUaServerInfo>> GetServerInfoAsync();

        /// <summary>
        /// Read node attributes
        /// </summary>
        Task<OpcUaOperationResult<OpcUaNodeAttributes>> ReadNodeAttributesAsync(NodeId nodeId);

        /// <summary>
        /// Read node history
        /// </summary>
        Task<OpcUaOperationResult<List<OpcUaHistoryValue>>> ReadHistoryAsync(NodeId nodeId, DateTime startTime, DateTime endTime, uint maxValues = 1000);

        /// <summary>
        /// Subscribe to events
        /// </summary>
        Task<OpcUaOperationResult<uint>> SubscribeToEventsAsync(NodeId? sourceNode = null, List<NodeId>? eventTypes = null);

        /// <summary>
        /// Create custom data type
        /// </summary>
        Task<OpcUaOperationResult> RegisterCustomDataTypeAsync(Type customType);

        /// <summary>
        /// Get namespace table
        /// </summary>
        NamespaceTable GetNamespaceTable();

        /// <summary>
        /// Translate browse paths to node IDs
        /// </summary>
        Task<OpcUaOperationResult<List<NodeId>>> TranslateBrowsePathsAsync(List<BrowsePath> browsePaths);

        /// <summary>
        /// Find servers on network
        /// </summary>
        Task<OpcUaOperationResult<List<ApplicationDescription>>> FindServersAsync(string? discoveryUrl = null);

        /// <summary>
        /// Find servers on network with discovery
        /// </summary>
        Task<OpcUaOperationResult<List<ServerOnNetwork>>> FindServersOnNetworkAsync(string? discoveryUrl = null);
    }
}
