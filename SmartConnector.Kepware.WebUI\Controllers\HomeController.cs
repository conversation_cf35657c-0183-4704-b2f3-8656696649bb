using Microsoft.AspNetCore.Mvc;
using SmartConnector.Kepware.WebUI.Services;
using SmartConnector.Kepware.WebUI.Models;

namespace SmartConnector.Kepware.WebUI.Controllers
{
    public class HomeController : Controller
    {
        private readonly KepwareDataService _kepwareDataService;
        private readonly ILogger<HomeController> _logger;

        public HomeController(KepwareDataService kepwareDataService, ILogger<HomeController> logger)
        {
            _kepwareDataService = kepwareDataService;
            _logger = logger;
        }

        /// <summary>
        /// Dashboard page
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var model = new DashboardViewModel
            {
                IsConnected = _kepwareDataService.IsConnected,
                ConnectionStatus = _kepwareDataService.IsConnected ? "Connected" : "Disconnected",
                LastUpdateTime = _kepwareDataService.LastUpdateTime,
                SubscribedTagsCount = _kepwareDataService.GetSubscribedTags().Count
            };

            if (_kepwareDataService.IsConnected)
            {
                try
                {
                    // Get server status
                    var statusResult = await _kepwareDataService.GetServerStatusAsync();
                    if (statusResult.IsSuccess && statusResult.Data != null)
                    {
                        model.ServerStatus = statusResult.Data;
                    }

                    // Get some sample tag values if any are subscribed
                    var subscribedTags = _kepwareDataService.GetSubscribedTags();
                    if (subscribedTags.Any())
                    {
                        var readResult = await _kepwareDataService.ReadTagsAsync(subscribedTags.Take(5).ToList());
                        if (readResult.IsSuccess && readResult.Data != null)
                        {
                            model.RecentTagValues = readResult.Data;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading dashboard data");
                    model.ErrorMessage = "Error loading dashboard data: " + ex.Message;
                }
            }

            return View(model);
        }

        /// <summary>
        /// Tag browser page
        /// </summary>
        public IActionResult Tags()
        {
            var model = new TagBrowserViewModel
            {
                IsConnected = _kepwareDataService.IsConnected,
                SubscribedTags = _kepwareDataService.GetSubscribedTags()
            };

            return View(model);
        }

        /// <summary>
        /// Real-time monitor page
        /// </summary>
        public IActionResult Monitor()
        {
            var model = new MonitorViewModel
            {
                IsConnected = _kepwareDataService.IsConnected,
                SubscribedTags = _kepwareDataService.GetSubscribedTags(),
                PollingInterval = _kepwareDataService.PollingIntervalMs
            };

            return View(model);
        }

        /// <summary>
        /// Settings page
        /// </summary>
        public IActionResult Settings()
        {
            var model = new SettingsViewModel
            {
                IsConnected = _kepwareDataService.IsConnected,
                LastUpdateTime = _kepwareDataService.LastUpdateTime,
                PollingInterval = _kepwareDataService.PollingIntervalMs
            };

            return View(model);
        }

        /// <summary>
        /// Error page
        /// </summary>
        public IActionResult Error()
        {
            return View();
        }
    }
}
