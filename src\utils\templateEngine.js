/**
 * Simple template engine for processing email and calendar templates
 */
class TemplateEngine {
  /**
   * Process template with data
   * @param {string} template - Template string with {{variable}} placeholders
   * @param {object} data - Data object to replace placeholders
   * @returns {string} Processed template
   */
  static process(template, data) {
    if (!template || typeof template !== 'string') {
      return template;
    }

    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      const value = this.getNestedValue(data, key);
      return value !== undefined ? value : match;
    });
  }

  /**
   * Get nested value from object using dot notation
   * @param {object} obj - Object to search
   * @param {string} path - Dot notation path (e.g., 'user.name')
   * @returns {any} Value at path or undefined
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Process email template
   * @param {object} template - Email template object
   * @param {object} data - Data for template processing
   * @returns {object} Processed email object
   */
  static processEmailTemplate(template, data) {
    const processedTemplate = {
      to: template.to || [],
      cc: template.cc || [],
      bcc: template.bcc || [],
      subject: this.process(template.subject, data),
      body: this.process(template.bodyTemplate || template.body, data),
      isHtml: template.isHtml || false
    };

    // Process recipient lists
    if (Array.isArray(template.to)) {
      processedTemplate.to = template.to.map(recipient => this.process(recipient, data));
    }

    return processedTemplate;
  }

  /**
   * Process calendar template
   * @param {object} template - Calendar template object
   * @param {object} data - Data for template processing
   * @returns {object} Processed calendar object
   */
  static processCalendarTemplate(template, data) {
    const now = new Date();
    const startTime = new Date(now.getTime() + (template.offsetHours || 0) * 60 * 60 * 1000);
    const endTime = new Date(startTime.getTime() + (template.duration || 60) * 60 * 1000);

    return {
      subject: this.process(template.subject, data),
      body: this.process(template.bodyTemplate || template.body, data),
      start: startTime,
      end: endTime,
      location: this.process(template.location || '', data),
      attendees: template.attendees || [],
      isAllDay: template.isAllDay || false
    };
  }

  /**
   * Format data for template processing
   * @param {object} tagData - Tag data from Kepware
   * @param {object} additionalData - Additional data to include
   * @returns {object} Formatted data object
   */
  static formatTagData(tagData, additionalData = {}) {
    const formatted = {
      tagId: tagData.tagId,
      value: tagData.value,
      previousValue: tagData.previousValue,
      timestamp: tagData.timestamp ? tagData.timestamp.toISOString() : new Date().toISOString(),
      date: new Date().toDateString(),
      time: new Date().toTimeString(),
      ...additionalData
    };

    // Add formatted timestamp variations
    const now = new Date();
    formatted.dateFormatted = now.toLocaleDateString();
    formatted.timeFormatted = now.toLocaleTimeString();
    formatted.datetimeFormatted = now.toLocaleString();

    return formatted;
  }

  /**
   * Generate production report content
   * @param {array} tagData - Array of tag data from Kepware
   * @param {string} format - Output format ('html' or 'text')
   * @returns {string} Formatted report content
   */
  static generateProductionReport(tagData, format = 'html') {
    if (!tagData || !Array.isArray(tagData)) {
      return 'No data available';
    }

    if (format === 'html') {
      let report = '<table border="1" style="border-collapse: collapse; width: 100%;">';
      report += '<tr style="background-color: #f2f2f2;"><th style="padding: 8px;">Metric</th><th style="padding: 8px;">Value</th><th style="padding: 8px;">Status</th></tr>';
      
      tagData.forEach(tag => {
        const metricName = tag.id ? tag.id.split('.').pop() : 'Unknown';
        const status = tag.s || 'UNKNOWN';
        const statusColor = status === 'GOOD' ? 'green' : 'red';
        
        report += `<tr>`;
        report += `<td style="padding: 8px;">${metricName}</td>`;
        report += `<td style="padding: 8px;">${tag.v || 'N/A'}</td>`;
        report += `<td style="padding: 8px; color: ${statusColor};">${status}</td>`;
        report += `</tr>`;
      });
      
      report += '</table>';
      return report;
    } else {
      let report = 'Production Report\n';
      report += '================\n\n';
      
      tagData.forEach(tag => {
        const metricName = tag.id ? tag.id.split('.').pop() : 'Unknown';
        report += `${metricName}: ${tag.v || 'N/A'} (${tag.s || 'UNKNOWN'})\n`;
      });
      
      return report;
    }
  }

  /**
   * Validate template syntax
   * @param {string} template - Template string to validate
   * @returns {object} Validation result
   */
  static validateTemplate(template) {
    const errors = [];
    const warnings = [];
    
    if (!template || typeof template !== 'string') {
      errors.push('Template must be a non-empty string');
      return { isValid: false, errors, warnings };
    }

    // Check for unmatched braces
    const openBraces = (template.match(/\{\{/g) || []).length;
    const closeBraces = (template.match(/\}\}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      errors.push('Unmatched template braces');
    }

    // Check for invalid variable names
    const variables = template.match(/\{\{(\w+)\}\}/g) || [];
    variables.forEach(variable => {
      const varName = variable.replace(/\{\{|\}\}/g, '');
      if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(varName)) {
        warnings.push(`Invalid variable name: ${varName}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      variables: variables.map(v => v.replace(/\{\{|\}\}/g, ''))
    };
  }
}

module.exports = TemplateEngine;
