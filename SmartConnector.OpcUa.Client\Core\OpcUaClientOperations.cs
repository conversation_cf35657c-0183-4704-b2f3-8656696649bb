using Microsoft.Extensions.Logging;
using Opc.Ua;
using Opc.Ua.Client;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// OPC UA client operations implementation (partial class)
    /// </summary>
    public partial class OpcUaClient
    {
        /// <summary>
        /// Write node values
        /// </summary>
        public async Task<OpcUaOperationResult> WriteAsync(List<OpcUaWriteValue> writeValues)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var nodesToWrite = writeValues.Select(wv => new WriteValue
                {
                    NodeId = wv.NodeId,
                    AttributeId = wv.AttributeId,
                    Value = new DataValue(new Variant(wv.Value)),
                    IndexRange = wv.IndexRange
                }).ToList();

                var response = await _session.WriteAsync(null, new WriteValueCollection(nodesToWrite));

                var hasErrors = false;
                var errorMessages = new List<string>();

                if (response?.Results != null)
                {
                    for (int i = 0; i < response.Results.Count; i++)
                    {
                        if (StatusCode.IsBad(response.Results[i]))
                        {
                            hasErrors = true;
                            errorMessages.Add($"Node {writeValues[i].NodeId}: {response.Results[i]}");
                        }
                    }
                }

                _logger.LogDebug("Wrote {Count} node values, {ErrorCount} errors", writeValues.Count, errorMessages.Count);

                return new OpcUaOperationResult
                {
                    IsSuccess = !hasErrors,
                    Message = hasErrors ? $"Write completed with {errorMessages.Count} errors" : $"Successfully wrote {writeValues.Count} values",
                    ErrorDetails = hasErrors ? string.Join("; ", errorMessages) : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing node values");
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Write failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Write single node value
        /// </summary>
        public async Task<OpcUaOperationResult> WriteAsync(NodeId nodeId, object value)
        {
            return await WriteAsync(new List<OpcUaWriteValue>
            {
                new OpcUaWriteValue { NodeId = nodeId, Value = value }
            });
        }

        /// <summary>
        /// Call method on server
        /// </summary>
        public async Task<OpcUaOperationResult<object[]>> CallMethodAsync(NodeId objectId, NodeId methodId, params object[] inputArguments)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<object[]>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var inputArgumentValues = inputArguments?.Select(arg => new Variant(arg)).ToArray() ?? Array.Empty<Variant>();

                var response = await _session.CallAsync(null, new CallMethodRequestCollection
                {
                    new CallMethodRequest
                    {
                        ObjectId = objectId,
                        MethodId = methodId,
                        InputArguments = new VariantCollection(inputArgumentValues)
                    }
                });

                if (response?.Results?.Count > 0)
                {
                    var result = response.Results[0];
                    
                    if (StatusCode.IsGood(result.StatusCode))
                    {
                        var outputArguments = result.OutputArguments?.Select(v => v.Value).ToArray() ?? Array.Empty<object>();
                        
                        _logger.LogDebug("Successfully called method {MethodId} on object {ObjectId}", methodId, objectId);
                        
                        return new OpcUaOperationResult<object[]>
                        {
                            IsSuccess = true,
                            Message = "Method called successfully",
                            Data = outputArguments
                        };
                    }
                    else
                    {
                        return new OpcUaOperationResult<object[]>
                        {
                            IsSuccess = false,
                            Message = "Method call failed",
                            StatusCode = result.StatusCode,
                            ErrorDetails = result.StatusCode.ToString()
                        };
                    }
                }

                return new OpcUaOperationResult<object[]>
                {
                    IsSuccess = false,
                    Message = "No response received"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling method {MethodId} on object {ObjectId}", methodId, objectId);
                
                return new OpcUaOperationResult<object[]>
                {
                    IsSuccess = false,
                    Message = "Method call failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Create subscription for data monitoring
        /// </summary>
        public async Task<OpcUaOperationResult<uint>> CreateSubscriptionAsync(OpcUaSubscriptionSettings settings)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult<uint>
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                var subscription = new Subscription(_session.DefaultSubscription)
                {
                    DisplayName = settings.DisplayName,
                    PublishingInterval = (int)settings.PublishingInterval,
                    LifetimeCount = settings.LifetimeCount,
                    MaxKeepAliveCount = settings.MaxKeepAliveCount,
                    MaxNotificationsPerPublish = settings.MaxNotificationsPerPublish,
                    PublishingEnabled = settings.PublishingEnabled,
                    Priority = settings.Priority
                };

                _session.AddSubscription(subscription);
                await subscription.CreateAsync();

                lock (_lock)
                {
                    _subscriptions[subscription.Id] = subscription;
                }

                _logger.LogInformation("Created subscription {SubscriptionId} with name '{DisplayName}'", subscription.Id, settings.DisplayName);

                return new OpcUaOperationResult<uint>
                {
                    IsSuccess = true,
                    Message = "Subscription created successfully",
                    Data = subscription.Id
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription");
                
                return new OpcUaOperationResult<uint>
                {
                    IsSuccess = false,
                    Message = "Subscription creation failed",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Add monitored items to subscription
        /// </summary>
        public async Task<OpcUaOperationResult> AddMonitoredItemsAsync(uint subscriptionId, List<OpcUaMonitoredItem> monitoredItems)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                lock (_lock)
                {
                    if (!_subscriptions.TryGetValue(subscriptionId, out var subscription))
                    {
                        return new OpcUaOperationResult
                        {
                            IsSuccess = false,
                            Message = $"Subscription {subscriptionId} not found"
                        };
                    }

                    var itemsToAdd = new List<MonitoredItem>();

                    foreach (var item in monitoredItems)
                    {
                        var monitoredItem = new MonitoredItem(subscription.DefaultItem)
                        {
                            DisplayName = item.DisplayName ?? item.NodeId.ToString(),
                            StartNodeId = item.NodeId,
                            AttributeId = item.AttributeId,
                            IndexRange = item.IndexRange,
                            MonitoringMode = item.MonitoringMode,
                            SamplingInterval = (int)item.SamplingInterval,
                            QueueSize = item.QueueSize,
                            DiscardOldest = item.DiscardOldest,
                            Filter = item.Filter
                        };

                        // Set client handle
                        monitoredItem.Handle = item.ClientHandle;

                        itemsToAdd.Add(monitoredItem);
                    }

                    subscription.AddItems(itemsToAdd);
                    subscription.ApplyChanges();
                }

                _logger.LogDebug("Added {Count} monitored items to subscription {SubscriptionId}", monitoredItems.Count, subscriptionId);

                return new OpcUaOperationResult
                {
                    IsSuccess = true,
                    Message = $"Added {monitoredItems.Count} monitored items"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding monitored items to subscription {SubscriptionId}", subscriptionId);
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to add monitored items",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Remove monitored items from subscription
        /// </summary>
        public async Task<OpcUaOperationResult> RemoveMonitoredItemsAsync(uint subscriptionId, List<uint> monitoredItemIds)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                lock (_lock)
                {
                    if (!_subscriptions.TryGetValue(subscriptionId, out var subscription))
                    {
                        return new OpcUaOperationResult
                        {
                            IsSuccess = false,
                            Message = $"Subscription {subscriptionId} not found"
                        };
                    }

                    var itemsToRemove = new List<MonitoredItem>();

                    foreach (var itemId in monitoredItemIds)
                    {
                        var item = subscription.MonitoredItems.FirstOrDefault(mi => (uint)mi.Handle == itemId);
                        if (item != null)
                        {
                            itemsToRemove.Add(item);
                        }
                    }

                    subscription.RemoveItems(itemsToRemove);
                    subscription.ApplyChanges();
                }

                _logger.LogDebug("Removed {Count} monitored items from subscription {SubscriptionId}", monitoredItemIds.Count, subscriptionId);

                return new OpcUaOperationResult
                {
                    IsSuccess = true,
                    Message = $"Removed {monitoredItemIds.Count} monitored items"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing monitored items from subscription {SubscriptionId}", subscriptionId);
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to remove monitored items",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// Delete subscription
        /// </summary>
        public async Task<OpcUaOperationResult> DeleteSubscriptionAsync(uint subscriptionId)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return new OpcUaOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to server"
                    };
                }

                lock (_lock)
                {
                    if (!_subscriptions.TryGetValue(subscriptionId, out var subscription))
                    {
                        return new OpcUaOperationResult
                        {
                            IsSuccess = false,
                            Message = $"Subscription {subscriptionId} not found"
                        };
                    }

                    _session.RemoveSubscription(subscription);
                    subscription.Delete(true);
                    _subscriptions.Remove(subscriptionId);
                }

                _logger.LogInformation("Deleted subscription {SubscriptionId}", subscriptionId);

                return new OpcUaOperationResult
                {
                    IsSuccess = true,
                    Message = "Subscription deleted successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subscription {SubscriptionId}", subscriptionId);
                
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = "Failed to delete subscription",
                    ErrorDetails = ex.Message,
                    Exception = ex
                };
            }
        }
    }
}
