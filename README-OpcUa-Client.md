# SmartConnector.OpcUa.Client

A comprehensive, full-featured OPC UA client library for .NET with advanced industrial automation capabilities.

## Overview

This library provides a complete OPC UA client implementation with all the features needed for industrial automation, SCADA systems, and IoT applications. It's built on the official OPC Foundation .NET Standard library and provides a high-level, easy-to-use API.

## 🚀 **Key Features**

### 🔧 **Core OPC UA Operations**
- **Connection Management**: Connect, disconnect, reconnect with auto-retry
- **Node Browsing**: Browse server address space recursively
- **Data Access**: Read/write single or multiple node values
- **Method Calls**: Execute server methods with input/output parameters
- **Subscriptions**: Real-time data monitoring with configurable sampling
- **Events**: Subscribe to server events and alarms
- **History**: Read historical data from servers

### 🛡️ **Security & Authentication**
- **Multiple Security Policies**: None, Basic128Rsa15, Basic256, Basic256Sha256
- **Message Security**: None, Sign, SignAndEncrypt
- **User Authentication**: Anonymous, Username/Password, X.509 Certificate
- **Certificate Management**: Auto-create, validate, and manage certificates
- **Trust Lists**: Manage trusted/rejected certificate stores

### 📊 **Advanced Features**
- **Server Discovery**: Find servers on network and get endpoints
- **Complex Data Types**: Support for custom and structured data types
- **Namespace Management**: Handle multiple namespaces
- **Browse Path Translation**: Convert browse paths to NodeIds
- **Node Attributes**: Read all node attributes (metadata)
- **Quality Codes**: Full StatusCode support and handling

### 🔄 **Background Service**
- **Hosted Service**: .NET Core background service integration
- **Auto-Reconnection**: Automatic reconnection with configurable retry
- **Health Monitoring**: Built-in health checks and monitoring
- **Event-Driven**: Real-time notifications for data changes
- **Configuration-Based**: JSON/XML configuration support

### 🏗️ **Architecture**
- **Dependency Injection**: Full .NET Core DI container support
- **Async/Await**: Modern async programming patterns
- **Logging**: Structured logging with Microsoft.Extensions.Logging
- **Configuration**: Flexible configuration system
- **Extensible**: Easy to extend and customize

## Installation

### NuGet Package
```bash
Install-Package SmartConnector.OpcUa.Client
```

### Manual Build
```bash
git clone <repository>
cd SmartConnector.OpcUa.Client
dotnet build
dotnet pack
```

## Quick Start

### 1. Basic Usage

```csharp
using SmartConnector.OpcUa.Client;
using Microsoft.Extensions.Logging;

// Create configuration
var config = new OpcUaClientConfiguration
{
    EndpointUrl = "opc.tcp://localhost:4840",
    ApplicationName = "My OPC UA Client",
    Security = new OpcUaSecurityConfiguration
    {
        SecurityPolicy = SecurityPolicies.None,
        MessageSecurityMode = MessageSecurityMode.None,
        UserIdentityType = UserTokenType.Anonymous
    }
};

// Create logger
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<OpcUaClient>();

// Create and use client
using var client = new OpcUaClient(config, logger);

// Connect
var connectResult = await client.ConnectAsync();
if (connectResult.IsSuccess)
{
    Console.WriteLine("Connected successfully!");
    
    // Read a value
    var readResult = await client.ReadAsync(new NodeId("ns=2;s=Temperature"));
    if (readResult.IsSuccess)
    {
        Console.WriteLine($"Temperature: {readResult.Data?.Value}");
    }
    
    // Browse nodes
    var browseResult = await client.BrowseAsync();
    if (browseResult.IsSuccess)
    {
        foreach (var node in browseResult.Data ?? new List<OpcUaNode>())
        {
            Console.WriteLine($"{node.DisplayName} ({node.NodeClass})");
        }
    }
    
    await client.DisconnectAsync();
}
```

### 2. Background Service with Dependency Injection

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SmartConnector.OpcUa.Client.Extensions;

// Create host
var host = Host.CreateDefaultBuilder()
    .ConfigureServices((context, services) =>
    {
        // Add logging
        services.AddLogging(builder => builder.AddConsole());
        
        // Configure OPC UA client
        var clientConfig = new OpcUaClientConfiguration
        {
            EndpointUrl = "opc.tcp://localhost:4840",
            ApplicationName = "Background Service Example"
        };
        
        var serviceConfig = new OpcUaClientServiceConfiguration
        {
            DataSubscriptions = new List<OpcUaDataSubscription>
            {
                new OpcUaDataSubscription
                {
                    Name = "Production Data",
                    PublishingInterval = 1000,
                    MonitoredItems = new List<OpcUaMonitoredItemConfig>
                    {
                        new OpcUaMonitoredItemConfig
                        {
                            NodeId = new NodeId("ns=2;s=Temperature"),
                            DisplayName = "Temperature",
                            SamplingInterval = 500
                        }
                    }
                }
            }
        };
        
        // Add OPC UA services
        services.AddOpcUaClient(clientConfig, serviceConfig);
    })
    .Build();

// Subscribe to events
var opcUaService = host.Services.GetService<OpcUaClientService>();
opcUaService.DataChanged += (sender, e) =>
{
    foreach (var item in e.ChangedItems)
    {
        Console.WriteLine($"{item.DisplayName}: {item.Value}");
    }
};

// Run the service
await host.RunAsync();
```

### 3. Advanced Operations

```csharp
// Read multiple nodes
var nodeIds = new List<NodeId>
{
    new NodeId("ns=2;s=Temperature"),
    new NodeId("ns=2;s=Pressure"),
    new NodeId("ns=2;s=FlowRate")
};

var readResult = await client.ReadAsync(nodeIds);
if (readResult.IsSuccess)
{
    foreach (var value in readResult.Data ?? new List<OpcUaValue>())
    {
        Console.WriteLine($"{value.NodeId}: {value.Value} ({value.StatusCode})");
    }
}

// Write values
var writeValues = new List<OpcUaWriteValue>
{
    new OpcUaWriteValue { NodeId = new NodeId("ns=2;s=Setpoint"), Value = 25.5 },
    new OpcUaWriteValue { NodeId = new NodeId("ns=2;s=Mode"), Value = "Auto" }
};

var writeResult = await client.WriteAsync(writeValues);
if (writeResult.IsSuccess)
{
    Console.WriteLine("Values written successfully");
}

// Call method
var methodResult = await client.CallMethodAsync(
    new NodeId("ns=2;s=Machine"), 
    new NodeId("ns=2;s=StartProduction"), 
    "Recipe1", 100);

if (methodResult.IsSuccess)
{
    Console.WriteLine($"Method returned: {string.Join(", ", methodResult.Data ?? new object[0])}");
}

// Create subscription
var subscriptionSettings = new OpcUaSubscriptionSettings
{
    DisplayName = "Real-time Data",
    PublishingInterval = 1000
};

var subResult = await client.CreateSubscriptionAsync(subscriptionSettings);
if (subResult.IsSuccess)
{
    var subscriptionId = subResult.Data!.Value;
    
    // Add monitored items
    var monitoredItems = new List<OpcUaMonitoredItem>
    {
        new OpcUaMonitoredItem
        {
            ClientHandle = 1,
            NodeId = new NodeId("ns=2;s=Temperature"),
            SamplingInterval = 500
        }
    };
    
    await client.AddMonitoredItemsAsync(subscriptionId, monitoredItems);
}
```

## Configuration

### Client Configuration
```csharp
var config = new OpcUaClientConfiguration
{
    EndpointUrl = "opc.tcp://localhost:4840",
    ApplicationName = "My OPC UA Client",
    ApplicationUri = "urn:MyCompany:MyClient",
    
    Security = new OpcUaSecurityConfiguration
    {
        SecurityPolicy = SecurityPolicies.Basic256Sha256,
        MessageSecurityMode = MessageSecurityMode.SignAndEncrypt,
        UserIdentityType = UserTokenType.UserName,
        Username = "operator",
        Password = "password"
    },
    
    Session = new OpcUaSessionConfiguration
    {
        SessionTimeout = 60000,
        MaxRequestMessageSize = 4 * 1024 * 1024
    },
    
    Connection = new OpcUaConnectionConfiguration
    {
        ConnectionTimeout = 30000,
        OperationTimeout = 30000,
        AutoReconnect = true,
        ReconnectPeriod = 10000
    }
};
```

### JSON Configuration
```json
{
  "OpcUaClient": {
    "EndpointUrl": "opc.tcp://localhost:4840",
    "ApplicationName": "SmartConnector OPC UA Client",
    "Security": {
      "SecurityPolicy": "http://opcfoundation.org/UA/SecurityPolicy#None",
      "MessageSecurityMode": "None",
      "UserIdentityType": "Anonymous"
    }
  },
  "OpcUaClientService": {
    "HealthCheckInterval": 30000,
    "DataSubscriptions": [
      {
        "Name": "Production Monitoring",
        "PublishingInterval": 1000,
        "MonitoredItems": [
          {
            "NodeId": "ns=2;s=Temperature",
            "DisplayName": "Temperature",
            "SamplingInterval": 500
          }
        ]
      }
    ]
  }
}
```

## API Reference

### IOpcUaClient Interface

```csharp
public interface IOpcUaClient : IDisposable
{
    bool IsConnected { get; }
    Session? Session { get; }
    
    // Events
    event EventHandler<OpcUaConnectionEventArgs>? ConnectionStatusChanged;
    event EventHandler<OpcUaNotificationEventArgs>? DataChanged;
    event EventHandler<OpcUaEventNotificationArgs>? EventReceived;
    
    // Connection
    Task<OpcUaOperationResult> ConnectAsync();
    Task<OpcUaOperationResult> DisconnectAsync();
    Task<OpcUaOperationResult> ReconnectAsync();
    
    // Browsing
    Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseAsync(NodeId? startingNode = null);
    Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseRecursiveAsync(NodeId? startingNode = null, int maxDepth = 5);
    
    // Data Access
    Task<OpcUaOperationResult<List<OpcUaValue>>> ReadAsync(List<NodeId> nodeIds);
    Task<OpcUaOperationResult<OpcUaValue>> ReadAsync(NodeId nodeId);
    Task<OpcUaOperationResult> WriteAsync(List<OpcUaWriteValue> writeValues);
    Task<OpcUaOperationResult> WriteAsync(NodeId nodeId, object value);
    
    // Methods
    Task<OpcUaOperationResult<object[]>> CallMethodAsync(NodeId objectId, NodeId methodId, params object[] inputArguments);
    
    // Subscriptions
    Task<OpcUaOperationResult<uint>> CreateSubscriptionAsync(OpcUaSubscriptionSettings settings);
    Task<OpcUaOperationResult> AddMonitoredItemsAsync(uint subscriptionId, List<OpcUaMonitoredItem> monitoredItems);
    Task<OpcUaOperationResult> DeleteSubscriptionAsync(uint subscriptionId);
    
    // Advanced
    Task<OpcUaOperationResult<List<OpcUaHistoryValue>>> ReadHistoryAsync(NodeId nodeId, DateTime startTime, DateTime endTime);
    Task<OpcUaOperationResult<uint>> SubscribeToEventsAsync(NodeId? sourceNode = null);
    Task<OpcUaOperationResult<List<ApplicationDescription>>> FindServersAsync(string? discoveryUrl = null);
}
```

## Example Use Cases

### 1. Industrial Data Collection
```csharp
// Monitor production line data
var productionNodes = new List<NodeId>
{
    new NodeId("ns=2;s=Line1.Speed"),
    new NodeId("ns=2;s=Line1.Temperature"),
    new NodeId("ns=2;s=Line1.Pressure"),
    new NodeId("ns=2;s=Line1.ProductCount")
};

var values = await client.ReadAsync(productionNodes);
foreach (var value in values.Data ?? new List<OpcUaValue>())
{
    // Store in database, send to cloud, etc.
    await StoreProductionData(value.NodeId.ToString(), value.Value, value.SourceTimestamp);
}
```

### 2. Alarm Monitoring
```csharp
// Subscribe to alarm events
var alarmSubscription = await client.SubscribeToEventsAsync(ObjectIds.Server);

client.EventReceived += (sender, e) =>
{
    if (e.Severity >= 500) // High severity alarm
    {
        // Send notification, log alarm, etc.
        await SendAlarmNotification(e.Message, e.Severity);
    }
};
```

### 3. Recipe Management
```csharp
// Download recipe to PLC
var recipeData = await GetRecipeFromDatabase("Recipe123");

var writeOperations = new List<OpcUaWriteValue>
{
    new OpcUaWriteValue { NodeId = new NodeId("ns=2;s=Recipe.Temperature"), Value = recipeData.Temperature },
    new OpcUaWriteValue { NodeId = new NodeId("ns=2;s=Recipe.Time"), Value = recipeData.Time },
    new OpcUaWriteValue { NodeId = new NodeId("ns=2;s=Recipe.Speed"), Value = recipeData.Speed }
};

await client.WriteAsync(writeOperations);

// Start production
await client.CallMethodAsync(
    new NodeId("ns=2;s=ProductionLine"), 
    new NodeId("ns=2;s=StartProduction"));
```

## Building the Library

```bash
cd SmartConnector.OpcUa.Client
dotnet build -c Release
```

The compiled DLL will be available in:
`SmartConnector.OpcUa.Client/bin/Release/net6.0/SmartConnector.OpcUa.Client.dll`

## Dependencies

- OPCFoundation.NetStandard.Opc.Ua (1.4.371.86)
- OPCFoundation.NetStandard.Opc.Ua.Client (1.4.371.86)
- Microsoft.Extensions.Hosting (7.0.1)
- Microsoft.Extensions.Logging (7.0.0)
- Microsoft.Extensions.DependencyInjection (7.0.0)

## License

MIT License - see LICENSE file for details.

## Support

This library provides a complete, production-ready OPC UA client solution for industrial automation applications with full feature support and enterprise-grade reliability.
