using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmartConnector.Ews.Server.Services;

namespace SmartConnector.Ews.Server.Extensions
{
    /// <summary>
    /// Service collection extensions for SmartConnector EWS Server
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add SmartConnector EWS services to the service collection
        /// </summary>
        public static IServiceCollection AddSmartConnectorEws(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure EWS
            var ewsConfig = new EwsConfiguration();
            configuration.GetSection("EwsConfiguration").Bind(ewsConfig);
            services.AddSingleton(ewsConfig);

            // Configure Kepware
            var kepwareConfig = new KepwareConfiguration();
            configuration.GetSection("KepwareConfiguration").Bind(kepwareConfig);
            services.AddSingleton(kepwareConfig);

            // Configure SmartConnector
            services.Configure<SmartConnectorConfiguration>(
                configuration.GetSection("SmartConnectorConfiguration"));

            // Register services
            services.AddSingleton<IEwsServer>(provider =>
            {
                var config = provider.GetRequiredService<EwsConfiguration>();
                var logger = provider.GetRequiredService<ILogger<EwsServer>>();
                return new EwsServer(config, logger);
            });

            services.AddSingleton<KepwareConnector>(provider =>
            {
                var config = provider.GetRequiredService<KepwareConfiguration>();
                var logger = provider.GetRequiredService<ILogger<KepwareConnector>>();
                var httpClient = provider.GetService<HttpClient>();
                return new KepwareConnector(config, logger, httpClient);
            });

            services.AddHostedService<SmartConnectorService>();

            // Add HTTP client for Kepware
            services.AddHttpClient();

            return services;
        }

        /// <summary>
        /// Add SmartConnector EWS services with custom configurations
        /// </summary>
        public static IServiceCollection AddSmartConnectorEws(
            this IServiceCollection services,
            EwsConfiguration ewsConfig,
            KepwareConfiguration kepwareConfig,
            SmartConnectorConfiguration? smartConnectorConfig = null)
        {
            // Register configurations
            services.AddSingleton(ewsConfig);
            services.AddSingleton(kepwareConfig);
            
            if (smartConnectorConfig != null)
            {
                services.AddSingleton(smartConnectorConfig);
            }

            // Register services
            services.AddSingleton<IEwsServer>(provider =>
            {
                var config = provider.GetRequiredService<EwsConfiguration>();
                var logger = provider.GetRequiredService<ILogger<EwsServer>>();
                return new EwsServer(config, logger);
            });

            services.AddSingleton<KepwareConnector>(provider =>
            {
                var config = provider.GetRequiredService<KepwareConfiguration>();
                var logger = provider.GetRequiredService<ILogger<KepwareConnector>>();
                var httpClient = provider.GetService<HttpClient>();
                return new KepwareConnector(config, logger, httpClient);
            });

            services.AddHostedService<SmartConnectorService>();

            // Add HTTP client for Kepware
            services.AddHttpClient();

            return services;
        }
    }
}
