using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace SmartConnector.Ews.Server.Services
{
    /// <summary>
    /// Main SmartConnector service that bridges Kepware and EWS
    /// </summary>
    public class SmartConnectorService : BackgroundService
    {
        private readonly ILogger<SmartConnectorService> _logger;
        private readonly IEwsServer _ewsServer;
        private readonly KepwareConnector _kepwareConnector;
        private readonly SmartConnectorConfiguration _config;
        private readonly List<ConnectorRule> _rules;
        private readonly Dictionary<string, object?> _lastTagValues;
        private readonly Timer _pollingTimer;

        public SmartConnectorService(
            IEwsServer ewsServer,
            KepwareConnector kepwareConnector,
            IOptions<SmartConnectorConfiguration> config,
            ILogger<SmartConnectorService> logger)
        {
            _ewsServer = ewsServer ?? throw new ArgumentNullException(nameof(ewsServer));
            _kepwareConnector = kepwareConnector ?? throw new ArgumentNullException(nameof(kepwareConnector));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _rules = new List<ConnectorRule>();
            _lastTagValues = new Dictionary<string, object?>();
            
            // Initialize polling timer
            _pollingTimer = new Timer(PollTagsCallback, null, Timeout.Infinite, Timeout.Infinite);
            
            LoadRules();
        }

        /// <summary>
        /// Load connector rules from configuration
        /// </summary>
        private void LoadRules()
        {
            // Add default alarm notification rule
            _rules.Add(new ConnectorRule
            {
                Id = "alarm-notification",
                Name = "Alarm Notification",
                TagId = "Channel1.Device1.AlarmStatus",
                RuleType = ConnectorRuleType.TagChange,
                Condition = (current, previous) => 
                {
                    return current is bool currentBool && 
                           currentBool && 
                           (previous == null || !(bool)previous);
                },
                Action = async (tagValue) =>
                {
                    await _ewsServer.SendEmailAsync(new EwsEmailMessage
                    {
                        To = new List<string> { "<EMAIL>" },
                        Subject = "ALARM: Equipment Alert",
                        Body = $"Alarm triggered at {DateTime.Now:yyyy-MM-dd HH:mm:ss}\nTag: {tagValue.TagId}\nValue: {tagValue.Value}",
                        Priority = EwsImportance.High
                    });
                },
                IsEnabled = true
            });

            // Add maintenance scheduling rule
            _rules.Add(new ConnectorRule
            {
                Id = "maintenance-schedule",
                Name = "Maintenance Schedule",
                TagId = "Channel1.Device1.MaintenanceRequired",
                RuleType = ConnectorRuleType.TagChange,
                Condition = (current, previous) => 
                {
                    return current is bool currentBool && 
                           currentBool && 
                           (previous == null || !(bool)previous);
                },
                Action = async (tagValue) =>
                {
                    var tomorrow = DateTime.Now.AddDays(1).Date.AddHours(9); // 9 AM tomorrow
                    var endTime = tomorrow.AddHours(1); // 1 hour duration

                    await _ewsServer.CreateAppointmentAsync(new EwsAppointment
                    {
                        Subject = "Equipment Maintenance Required",
                        Body = $"Maintenance required for equipment.\nTag: {tagValue.TagId}\nTriggered: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                        Start = tomorrow,
                        End = endTime,
                        Location = "Production Floor",
                        RequiredAttendees = new List<string> { "<EMAIL>", "<EMAIL>" },
                        Priority = EwsImportance.High
                    });
                },
                IsEnabled = true
            });

            _logger.LogInformation("Loaded {RuleCount} connector rules", _rules.Count);
        }

        /// <summary>
        /// Start the service
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("SmartConnector service starting...");

            // Test connections
            var ewsTest = await _ewsServer.TestConnectionAsync();
            var kepwareTest = await _kepwareConnector.TestConnectionAsync();

            if (!ewsTest.IsSuccess)
            {
                _logger.LogError("EWS connection failed: {Message}", ewsTest.Message);
                return;
            }

            if (!kepwareTest.IsSuccess)
            {
                _logger.LogError("Kepware connection failed: {Message}", kepwareTest.Message);
                return;
            }

            _logger.LogInformation("All connections established successfully");

            // Start polling
            _pollingTimer.Change(TimeSpan.Zero, TimeSpan.FromMilliseconds(_config.PollingIntervalMs));

            // Keep service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }

        /// <summary>
        /// Timer callback for polling tags
        /// </summary>
        private async void PollTagsCallback(object? state)
        {
            try
            {
                var tagIds = _rules.Where(r => r.RuleType == ConnectorRuleType.TagChange && r.IsEnabled)
                                  .Select(r => r.TagId)
                                  .Distinct()
                                  .ToList();

                if (tagIds.Count == 0)
                {
                    return;
                }

                var result = await _kepwareConnector.ReadTagsAsync(tagIds);
                
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (var tagValue in result.Data)
                    {
                        await ProcessTagValue(tagValue);
                    }
                }
                else
                {
                    _logger.LogWarning("Failed to read tags: {Message}", result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during tag polling");
            }
        }

        /// <summary>
        /// Process individual tag value and execute rules
        /// </summary>
        private async Task ProcessTagValue(KepwareTagValue tagValue)
        {
            try
            {
                var previousValue = _lastTagValues.ContainsKey(tagValue.TagId) 
                    ? _lastTagValues[tagValue.TagId] 
                    : null;

                _lastTagValues[tagValue.TagId] = tagValue.Value;

                // Find applicable rules
                var applicableRules = _rules.Where(r => 
                    r.TagId == tagValue.TagId && 
                    r.RuleType == ConnectorRuleType.TagChange && 
                    r.IsEnabled).ToList();

                foreach (var rule in applicableRules)
                {
                    try
                    {
                        if (rule.Condition(tagValue.Value, previousValue))
                        {
                            _logger.LogInformation("Executing rule '{RuleName}' for tag '{TagId}'", rule.Name, tagValue.TagId);
                            await rule.Action(tagValue);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error executing rule '{RuleName}' for tag '{TagId}'", rule.Name, tagValue.TagId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing tag value for '{TagId}'", tagValue.TagId);
            }
        }

        /// <summary>
        /// Stop the service
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("SmartConnector service stopping...");
            
            _pollingTimer.Change(Timeout.Infinite, Timeout.Infinite);
            
            await base.StopAsync(cancellationToken);
            
            _logger.LogInformation("SmartConnector service stopped");
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public override void Dispose()
        {
            _pollingTimer?.Dispose();
            _kepwareConnector?.Dispose();
            base.Dispose();
        }
    }

    /// <summary>
    /// SmartConnector configuration
    /// </summary>
    public class SmartConnectorConfiguration
    {
        public int PollingIntervalMs { get; set; } = 5000;
        public int MaxRetries { get; set; } = 3;
        public int TimeoutMs { get; set; } = 30000;
        public bool EnableLogging { get; set; } = true;
        public string LogLevel { get; set; } = "Information";
    }

    /// <summary>
    /// Connector rule definition
    /// </summary>
    public class ConnectorRule
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string TagId { get; set; } = string.Empty;
        public ConnectorRuleType RuleType { get; set; }
        public Func<object?, object?, bool> Condition { get; set; } = (current, previous) => false;
        public Func<KepwareTagValue, Task> Action { get; set; } = _ => Task.CompletedTask;
        public bool IsEnabled { get; set; } = true;
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastExecuted { get; set; }
    }

    /// <summary>
    /// Connector rule types
    /// </summary>
    public enum ConnectorRuleType
    {
        TagChange,
        Scheduled,
        Manual
    }
}
