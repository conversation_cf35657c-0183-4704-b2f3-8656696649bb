/* SmartConnector OPC UA Web UI Styles */

html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Custom styles for OPC UA interface */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.main-content {
    margin-left: 240px;
    padding: 20px;
}

.connection-status {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.connected {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.disconnected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.node-tree {
    max-height: 500px;
    overflow-y: auto;
}

.node-item {
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
}

.node-item:hover {
    background-color: #f8f9fa;
}

.value-card {
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-good { background-color: #28a745; }
.status-bad { background-color: #dc3545; }
.status-uncertain { background-color: #ffc107; }

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    .main-content {
        margin-left: 0;
        padding: 10px;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom button styles */
.btn-outline-primary:hover {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Table styles */
.table-responsive {
    border-radius: 0.375rem;
}

/* Card styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Alert styles */
.alert {
    border-radius: 0.375rem;
}

/* Form styles */
.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Navigation styles */
.nav-link.active {
    font-weight: 600;
}

/* Utility classes */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.border-start {
    border-left: 1px solid #dee2e6 !important;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* Animation for data updates */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.data-update {
    animation: pulse 0.5s ease-in-out;
}

/* Custom scrollbar */
.node-tree::-webkit-scrollbar {
    width: 8px;
}

.node-tree::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.node-tree::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.node-tree::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Print styles */
@media print {
    .sidebar,
    .btn,
    .navbar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}
