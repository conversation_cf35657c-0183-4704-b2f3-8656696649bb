﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>SmartConnector.OpcUa.Client</id>
    <version>1.0.0</version>
    <authors>SmartConnector</authors>
    <description>Full-featured OPC UA client library for SmartConnector industrial automation integration</description>
    <copyright>Copyright © 2025</copyright>
    <tags>OPC-UA Industrial Automation SmartConnector SCADA PLC HMI</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Microsoft.Extensions.Configuration" version="7.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection" version="7.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Hosting" version="7.0.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="7.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Options" version="7.0.1" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.3" exclude="Build,Analyzers" />
        <dependency id="OPCFoundation.NetStandard.Opc.Ua" version="1.4.368.58" exclude="Build,Analyzers" />
        <dependency id="OPCFoundation.NetStandard.Opc.Ua.Client" version="1.4.368.58" exclude="Build,Analyzers" />
        <dependency id="System.Security.Cryptography.X509Certificates" version="4.3.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="G:\ua\client\SmartConnector.OpcUa.Client\bin\Debug\net6.0\SmartConnector.OpcUa.Client.dll" target="lib\net6.0\SmartConnector.OpcUa.Client.dll" />
  </files>
</package>