using Opc.Ua;
using Opc.Ua.Client;
using Opc.Ua.Configuration;
using SmartConnector.OpcUa.WebUI.Models;
using Microsoft.AspNetCore.SignalR;
using SmartConnector.OpcUa.WebUI.Hubs;

namespace SmartConnector.OpcUa.WebUI.Services
{
    public class RealOpcUaService
    {
        private readonly ILogger<RealOpcUaService> _logger;
        private readonly IHubContext<OpcUaHub> _hubContext;
        private Session? _session;
        private ApplicationConfiguration? _config;
        private bool _isConnected = false;
        private string? _endpointUrl;
        private readonly Dictionary<uint, Subscription> _subscriptions = new();
        private uint _nextSubscriptionId = 1;

        public bool IsConnected => _isConnected && _session?.Connected == true;
        public string? ServerUrl => _endpointUrl;

        public RealOpcUaService(ILogger<RealOpcUaService> logger, IHubContext<OpcUaHub> hubContext)
        {
            _logger = logger;
            _hubContext = hubContext;
        }

        public async Task<ServiceResult<bool>> ConnectAsync(string endpointUrl)
        {
            try
            {
                _logger.LogInformation("Connecting to real OPC UA server: {EndpointUrl}", endpointUrl);

                // Create application configuration
                _config = await CreateApplicationConfiguration();

                // Discover endpoints
                var endpointDescription = CoreClientUtils.SelectEndpoint(endpointUrl, false);
                var endpointConfiguration = EndpointConfiguration.Create(_config);
                var endpoint = new ConfiguredEndpoint(null, endpointDescription, endpointConfiguration);

                // Create session
                _session = await Session.Create(
                    _config,
                    endpoint,
                    false,
                    "SmartConnector OPC UA Client",
                    60000,
                    new UserIdentity(new AnonymousIdentityToken()),
                    null);

                if (_session != null && _session.Connected)
                {
                    _isConnected = true;
                    _endpointUrl = endpointUrl;
                    
                    // Setup session event handlers
                    _session.KeepAlive += Session_KeepAlive;
                    _session.Notification += Session_Notification;

                    _logger.LogInformation("Successfully connected to OPC UA server: {EndpointUrl}", endpointUrl);
                    return ServiceResult<bool>.Success(true);
                }
                else
                {
                    _logger.LogWarning("Failed to create session with OPC UA server");
                    return ServiceResult<bool>.Failure("Failed to create session");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to OPC UA server: {EndpointUrl}", endpointUrl);
                return ServiceResult<bool>.Failure($"Connection error: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> DisconnectAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting from OPC UA server");

                // Remove all subscriptions
                foreach (var subscription in _subscriptions.Values)
                {
                    _session?.RemoveSubscription(subscription);
                    subscription?.Dispose();
                }
                _subscriptions.Clear();

                // Close session
                if (_session != null)
                {
                    _session.KeepAlive -= Session_KeepAlive;
                    _session.Notification -= Session_Notification;
                    _session.Close();
                    _session.Dispose();
                    _session = null;
                }

                _isConnected = false;
                _endpointUrl = null;

                _logger.LogInformation("Successfully disconnected from OPC UA server");
                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from OPC UA server");
                return ServiceResult<bool>.Failure($"Disconnection error: {ex.Message}");
            }
        }

        public async Task<ServiceResult<IEnumerable<OpcUaDataValue>>> ReadAsync(IEnumerable<string> nodeIds)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return ServiceResult<IEnumerable<OpcUaDataValue>>.Failure("Not connected to server");
                }

                _logger.LogInformation("Reading {Count} nodes from OPC UA server", nodeIds.Count());

                var nodesToRead = new ReadValueIdCollection();
                foreach (var nodeId in nodeIds)
                {
                    nodesToRead.Add(new ReadValueId()
                    {
                        NodeId = new NodeId(nodeId),
                        AttributeId = Attributes.Value
                    });
                }

                // Read values
                _session.Read(null, 0, TimestampsToReturn.Both, nodesToRead, out var results, out var diagnosticInfos);

                var dataValues = new List<OpcUaDataValue>();
                for (int i = 0; i < results.Count; i++)
                {
                    var result = results[i];
                    dataValues.Add(new OpcUaDataValue
                    {
                        NodeId = nodeIds.ElementAt(i),
                        Value = result.Value?.ToString() ?? "null",
                        StatusCode = result.StatusCode.ToString(),
                        SourceTimestamp = result.SourceTimestamp,
                        ServerTimestamp = result.ServerTimestamp
                    });
                }

                _logger.LogInformation("Successfully read {Count} values from OPC UA server", dataValues.Count);
                return ServiceResult<IEnumerable<OpcUaDataValue>>.Success(dataValues);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading nodes from OPC UA server");
                return ServiceResult<IEnumerable<OpcUaDataValue>>.Failure($"Read error: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> WriteAsync(IEnumerable<MockWriteValue> writeValues)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return ServiceResult<bool>.Failure("Not connected to server");
                }

                _logger.LogInformation("Writing {Count} values to OPC UA server", writeValues.Count());

                var nodesToWrite = new WriteValueCollection();
                foreach (var writeValue in writeValues)
                {
                    nodesToWrite.Add(new WriteValue()
                    {
                        NodeId = new NodeId(writeValue.NodeId),
                        AttributeId = Attributes.Value,
                        Value = new DataValue(new Variant(writeValue.Value))
                    });
                }

                // Write values
                _session.Write(null, nodesToWrite, out var results, out var diagnosticInfos);

                // Check if all writes were successful
                bool allSuccess = results.All(r => StatusCode.IsGood(r));

                if (allSuccess)
                {
                    _logger.LogInformation("Successfully wrote {Count} values to OPC UA server", writeValues.Count());
                    return ServiceResult<bool>.Success(true);
                }
                else
                {
                    var errors = results.Where(r => !StatusCode.IsGood(r)).Select(r => r.ToString());
                    var errorMessage = string.Join(", ", errors);
                    _logger.LogWarning("Some write operations failed: {Errors}", errorMessage);
                    return ServiceResult<bool>.Failure($"Write errors: {errorMessage}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing values to OPC UA server");
                return ServiceResult<bool>.Failure($"Write error: {ex.Message}");
            }
        }

        public async Task<ServiceResult<IEnumerable<OpcUaNodeInfo>>> BrowseAsync(string? nodeId = null)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    return ServiceResult<IEnumerable<OpcUaNodeInfo>>.Failure("Not connected to server");
                }

                _logger.LogInformation("Browsing nodes from OPC UA server: {NodeId}", nodeId ?? "Root");

                var startingNode = string.IsNullOrEmpty(nodeId) ? ObjectIds.ObjectsFolder : new NodeId(nodeId);
                var nodesToBrowse = new BrowseDescriptionCollection()
                {
                    new BrowseDescription()
                    {
                        NodeId = startingNode,
                        BrowseDirection = BrowseDirection.Forward,
                        ReferenceTypeId = ReferenceTypeIds.HierarchicalReferences,
                        IncludeSubtypes = true,
                        NodeClassMask = 0,
                        ResultMask = (uint)(BrowseResultMask.All)
                    }
                };

                // Browse nodes
                _session.Browse(null, null, 0, nodesToBrowse, out var results, out var diagnosticInfos);

                var nodeInfos = new List<OpcUaNodeInfo>();
                if (results?.Count > 0 && results[0].References != null)
                {
                    foreach (var reference in results[0].References)
                    {
                        nodeInfos.Add(new OpcUaNodeInfo
                        {
                            NodeId = reference.NodeId.ToString(),
                            DisplayName = reference.DisplayName.Text,
                            BrowseName = reference.BrowseName.Name,
                            NodeClass = reference.NodeClass.ToString(),
                            HasChildren = reference.NodeClass == NodeClass.Object || reference.NodeClass == NodeClass.Variable
                        });
                    }
                }

                _logger.LogInformation("Successfully browsed {Count} nodes from OPC UA server", nodeInfos.Count);
                return ServiceResult<IEnumerable<OpcUaNodeInfo>>.Success(nodeInfos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error browsing nodes from OPC UA server");
                return ServiceResult<IEnumerable<OpcUaNodeInfo>>.Failure($"Browse error: {ex.Message}");
            }
        }

        public async Task<uint> CreateSubscriptionAsync(IEnumerable<string> nodeIds, double publishingInterval)
        {
            try
            {
                if (!IsConnected || _session == null)
                {
                    throw new InvalidOperationException("Not connected to server");
                }

                var subscriptionId = _nextSubscriptionId++;
                _logger.LogInformation("Creating subscription {SubscriptionId} for {Count} nodes", subscriptionId, nodeIds.Count());

                // Create subscription
                var subscription = new Subscription(_session.DefaultSubscription)
                {
                    PublishingInterval = (int)publishingInterval,
                    PublishingEnabled = true,
                    LifetimeCount = 0,
                    KeepAliveCount = 0,
                    DisplayName = $"Subscription_{subscriptionId}",
                    Priority = 0
                };

                // Add monitored items
                foreach (var nodeId in nodeIds)
                {
                    var monitoredItem = new MonitoredItem(subscription.DefaultItem)
                    {
                        DisplayName = nodeId,
                        StartNodeId = new NodeId(nodeId),
                        AttributeId = Attributes.Value,
                        MonitoringMode = MonitoringMode.Reporting,
                        SamplingInterval = (int)publishingInterval,
                        QueueSize = 1,
                        DiscardOldest = true
                    };

                    monitoredItem.Notification += MonitoredItem_Notification;
                    subscription.AddItem(monitoredItem);
                }

                // Add subscription to session
                _session.AddSubscription(subscription);
                subscription.Create();

                _subscriptions[subscriptionId] = subscription;

                _logger.LogInformation("Successfully created subscription {SubscriptionId}", subscriptionId);
                return subscriptionId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription");
                throw;
            }
        }

        private async Task<ApplicationConfiguration> CreateApplicationConfiguration()
        {
            var config = new ApplicationConfiguration()
            {
                ApplicationName = "SmartConnector OPC UA Client",
                ApplicationUri = Utils.Format(@"urn:{0}:SmartConnector:OpcUaClient", System.Net.Dns.GetHostName()),
                ApplicationType = ApplicationType.Client,
                SecurityConfiguration = new SecurityConfiguration
                {
                    ApplicationCertificate = new CertificateIdentifier { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\MachineDefault", SubjectName = Utils.Format(@"CN={0}, DC={1}", "SmartConnector", System.Net.Dns.GetHostName()) },
                    TrustedIssuerCertificates = new CertificateTrustList { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\UA Certificate Authorities" },
                    TrustedPeerCertificates = new CertificateTrustList { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\UA Applications" },
                    RejectedCertificateStore = new CertificateTrustList { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\RejectedCertificates" },
                    AutoAcceptUntrustedCertificates = true,
                    AddAppCertToTrustedStore = true
                },
                TransportConfigurations = new TransportConfigurationCollection(),
                TransportQuotas = new TransportQuotas { OperationTimeout = 15000 },
                ClientConfiguration = new ClientConfiguration { DefaultSessionTimeout = 60000 },
                TraceConfiguration = new TraceConfiguration()
            };

            config.TransportQuotas.OperationTimeout = 15000;
            config.TransportQuotas.MaxStringLength = 1048576;
            config.TransportQuotas.MaxByteStringLength = 1048576;
            config.TransportQuotas.MaxArrayLength = 65535;
            config.TransportQuotas.MaxMessageSize = 4194304;
            config.TransportQuotas.MaxBufferSize = 65535;
            config.TransportQuotas.ChannelLifetime = 300000;
            config.TransportQuotas.SecurityTokenLifetime = 3600000;

            await config.Validate(ApplicationType.Client);

            if (config.SecurityConfiguration.AutoAcceptUntrustedCertificates)
            {
                config.CertificateValidator.CertificateValidation += (s, e) => { e.Accept = (e.Error.StatusCode == Opc.Ua.StatusCodes.BadCertificateUntrusted); };
            }

            return config;
        }

        private void Session_KeepAlive(Session sender, KeepAliveEventArgs e)
        {
            if (e.Status != null && ServiceResult.IsNotGood(e.Status))
            {
                _logger.LogWarning("Session keep alive failed: {Status}", e.Status);
                _isConnected = false;
            }
        }

        private void Session_Notification(Session sender, NotificationEventArgs e)
        {
            // Handle session notifications if needed
        }

        private async void MonitoredItem_Notification(MonitoredItem item, MonitoredItemNotificationEventArgs e)
        {
            try
            {
                foreach (var value in item.DequeueValues())
                {
                    var dataChange = new OpcUaDataValue
                    {
                        NodeId = item.StartNodeId.ToString(),
                        Value = value.Value?.ToString() ?? "null",
                        StatusCode = value.StatusCode.ToString(),
                        SourceTimestamp = value.SourceTimestamp,
                        ServerTimestamp = value.ServerTimestamp,
                        DisplayName = item.DisplayName
                    };

                    // Send to SignalR clients
                    await _hubContext.Clients.All.SendAsync("DataChanged", new[] { dataChange });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling monitored item notification");
            }
        }

        public async Task StartAsync()
        {
            _logger.LogInformation("Real OPC UA Service starting...");
            await Task.CompletedTask;
        }

        public async Task StopAsync()
        {
            _logger.LogInformation("Real OPC UA Service stopping...");
            await DisconnectAsync();
        }
    }
}
