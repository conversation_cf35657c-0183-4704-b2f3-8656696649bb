namespace SmartConnector.Kepware.WebUI.Models
{
    /// <summary>
    /// Kepware configuration
    /// </summary>
    public class KepwareConfiguration
    {
        public string Host { get; set; } = "localhost";
        public int Port { get; set; } = 57412;
        public string Username { get; set; } = "Administrator";
        public string Password { get; set; } = string.Empty;
        public bool UseHttps { get; set; } = false;
        public string ApiVersion { get; set; } = "v1";
        public int TimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// Kepware operation result
    /// </summary>
    public class KepwareOperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? ErrorDetails { get; set; }
        public object? Data { get; set; }
    }

    /// <summary>
    /// Generic Kepware operation result
    /// </summary>
    public class KepwareOperationResult<T> : KepwareOperationResult
    {
        public new T? Data { get; set; }
    }

    /// <summary>
    /// Kepware tag value
    /// </summary>
    public class KepwareTagValue
    {
        public string TagId { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Quality { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Kepware tag write operation
    /// </summary>
    public class KepwareTagWrite
    {
        public string TagId { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    /// <summary>
    /// Kepware server status
    /// </summary>
    public class KepwareServerStatus
    {
        public string Status { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public long Uptime { get; set; }
        public int ConnectedClients { get; set; }
    }

    /// <summary>
    /// Kepware read response
    /// </summary>
    public class KepwareReadResponse
    {
        public List<KepwareReadResult>? ReadResults { get; set; }
    }

    /// <summary>
    /// Kepware read result
    /// </summary>
    public class KepwareReadResult
    {
        public string Id { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
