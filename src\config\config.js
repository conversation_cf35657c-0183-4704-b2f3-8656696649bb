const Joi = require('joi');

// Configuration schema validation
const configSchema = Joi.object({
  port: Joi.number().port().default(3000),
  nodeEnv: Joi.string().valid('development', 'production', 'test').default('development'),
  
  // Kepware configuration
  kepware: Joi.object({
    host: Joi.string().required(),
    port: Joi.number().port().default(57412),
    username: Joi.string().required(),
    password: Joi.string().required(),
    useHttps: Joi.boolean().default(false),
    apiVersion: Joi.string().default('v1'),
    timeout: Joi.number().default(30000)
  }).required(),
  
  // EWS configuration
  ews: Joi.object({
    url: Joi.string().uri().required(),
    username: Joi.string().required(),
    password: Joi.string().required(),
    domain: Joi.string().allow(''),
    version: Joi.string().default('Exchange2016')
  }).required(),
  
  // SmartConnector configuration
  connector: Joi.object({
    pollInterval: Joi.number().min(1000).default(5000),
    maxRetries: Joi.number().min(1).default(3),
    timeout: Joi.number().min(1000).default(30000)
  }).required(),
  
  // Logging configuration
  logging: Joi.object({
    level: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
    file: Joi.string().default('logs/smartconnector.log'),
    maxSize: Joi.string().default('10m'),
    maxFiles: Joi.number().default(5)
  }).required(),
  
  // Security configuration
  apiKey: Joi.string().required(),
  rateLimitWindow: Joi.number().default(15),
  rateLimitMaxRequests: Joi.number().default(100)
});

// Load and validate configuration
const rawConfig = {
  port: parseInt(process.env.PORT) || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  kepware: {
    host: process.env.KEPWARE_HOST || 'localhost',
    port: parseInt(process.env.KEPWARE_PORT) || 57412,
    username: process.env.KEPWARE_USERNAME || 'Administrator',
    password: process.env.KEPWARE_PASSWORD || '',
    useHttps: process.env.KEPWARE_USE_HTTPS === 'true',
    apiVersion: process.env.KEPWARE_API_VERSION || 'v1',
    timeout: parseInt(process.env.KEPWARE_TIMEOUT) || 30000
  },
  
  ews: {
    url: process.env.EWS_URL || '',
    username: process.env.EWS_USERNAME || '',
    password: process.env.EWS_PASSWORD || '',
    domain: process.env.EWS_DOMAIN || '',
    version: process.env.EWS_VERSION || 'Exchange2016'
  },
  
  connector: {
    pollInterval: parseInt(process.env.CONNECTOR_POLL_INTERVAL) || 5000,
    maxRetries: parseInt(process.env.CONNECTOR_MAX_RETRIES) || 3,
    timeout: parseInt(process.env.CONNECTOR_TIMEOUT) || 30000
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/smartconnector.log',
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
  },
  
  apiKey: process.env.API_KEY || 'your-api-key-here',
  rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 15,
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
};

// Validate configuration
const { error, value: config } = configSchema.validate(rawConfig);

if (error) {
  throw new Error(`Configuration validation error: ${error.details[0].message}`);
}

module.exports = config;
