using SmartConnector.OpcUa.Client;
using SmartConnector.OpcUa.Client.Services;
using SmartConnector.OpcUa.WebUI.Hubs;
using Microsoft.AspNetCore.SignalR;
using Opc.Ua;

namespace SmartConnector.OpcUa.WebUI.Services
{
    public class OpcUaWebService
    {
        private readonly IOpcUaClient _opcUaClient;
        private readonly IHubContext<OpcUaHub> _hubContext;
        private readonly ILogger<OpcUaWebService> _logger;
        private readonly Dictionary<uint, List<string>> _subscriptionNodes = new();

        public bool IsConnected => _opcUaClient.IsConnected;
        public string ServerUrl { get; private set; } = "opc.tcp://localhost:4840";

        public OpcUaWebService(IOpcUaClient opcUaClient, IHubContext<OpcUaHub> hubContext, ILogger<OpcUaWebService> logger)
        {
            _opcUaClient = opcUaClient;
            _hubContext = hubContext;
            _logger = logger;

            // Subscribe to client events
            _opcUaClient.ConnectionStatusChanged += OnConnectionStatusChanged;
            _opcUaClient.DataChanged += OnDataChanged;
            _opcUaClient.EventReceived += OnEventReceived;
            _opcUaClient.ErrorOccurred += OnErrorOccurred;
        }

        public async Task StartAsync()
        {
            _logger.LogInformation("OpcUaWebService starting...");
            
            // Try to connect to default server
            try
            {
                var result = await _opcUaClient.ConnectAsync();
                if (result.IsSuccess)
                {
                    _logger.LogInformation("Connected to default OPC UA server");
                }
                else
                {
                    _logger.LogWarning("Failed to connect to default server: {Message}", result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to default server");
            }
        }

        public async Task<OpcUaOperationResult> ConnectAsync(string endpointUrl)
        {
            try
            {
                // Disconnect if already connected
                if (_opcUaClient.IsConnected)
                {
                    await _opcUaClient.DisconnectAsync();
                }

                ServerUrl = endpointUrl;
                
                // Update client configuration with new endpoint
                // Note: In a full implementation, you'd recreate the client with new config
                var result = await _opcUaClient.ConnectAsync();
                
                if (result.IsSuccess)
                {
                    await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", true, "Connected successfully");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to {EndpointUrl}", endpointUrl);
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = ex.Message,
                    Exception = ex
                };
            }
        }

        public async Task<OpcUaOperationResult> DisconnectAsync()
        {
            try
            {
                var result = await _opcUaClient.DisconnectAsync();
                
                if (result.IsSuccess)
                {
                    _subscriptionNodes.Clear();
                    await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", false, "Disconnected");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting");
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = ex.Message,
                    Exception = ex
                };
            }
        }

        public async Task<OpcUaOperationResult<List<OpcUaNode>>> BrowseAsync(NodeId? startingNode = null)
        {
            return await _opcUaClient.BrowseAsync(startingNode);
        }

        public async Task<OpcUaOperationResult<List<OpcUaValue>>> ReadAsync(List<NodeId> nodeIds)
        {
            return await _opcUaClient.ReadAsync(nodeIds);
        }

        public async Task<OpcUaOperationResult> WriteAsync(List<OpcUaWriteValue> writeValues)
        {
            return await _opcUaClient.WriteAsync(writeValues);
        }

        public async Task<uint?> CreateSubscriptionAsync(List<NodeId> nodeIds, double publishingInterval = 1000)
        {
            try
            {
                var subscriptionSettings = new OpcUaSubscriptionSettings
                {
                    DisplayName = "Web UI Subscription",
                    PublishingInterval = publishingInterval
                };

                var subscriptionResult = await _opcUaClient.CreateSubscriptionAsync(subscriptionSettings);
                if (!subscriptionResult.IsSuccess || !subscriptionResult.Data.HasValue)
                {
                    return null;
                }

                var subscriptionId = subscriptionResult.Data.Value;

                var monitoredItems = nodeIds.Select((nodeId, index) => new OpcUaMonitoredItem
                {
                    ClientHandle = (uint)(subscriptionId * 1000 + index),
                    NodeId = nodeId,
                    SamplingInterval = publishingInterval,
                    QueueSize = 10
                }).ToList();

                var addItemsResult = await _opcUaClient.AddMonitoredItemsAsync(subscriptionId, monitoredItems);
                if (addItemsResult.IsSuccess)
                {
                    _subscriptionNodes[subscriptionId] = nodeIds.Select(n => n.ToString()).ToList();
                    return subscriptionId;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription");
                return null;
            }
        }

        public async Task<OpcUaOperationResult> DeleteSubscriptionAsync(uint subscriptionId)
        {
            try
            {
                var result = await _opcUaClient.DeleteSubscriptionAsync(subscriptionId);
                if (result.IsSuccess)
                {
                    _subscriptionNodes.Remove(subscriptionId);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subscription {SubscriptionId}", subscriptionId);
                return new OpcUaOperationResult
                {
                    IsSuccess = false,
                    Message = ex.Message,
                    Exception = ex
                };
            }
        }

        public async Task<OpcUaOperationResult<OpcUaServerInfo>> GetServerInfoAsync()
        {
            return await _opcUaClient.GetServerInfoAsync();
        }

        public async Task<List<OpcUaValue>?> GetRecentValuesAsync()
        {
            try
            {
                // Read some common server values
                var commonNodes = new List<NodeId>
                {
                    VariableIds.Server_ServerStatus_CurrentTime,
                    VariableIds.Server_ServerStatus_State,
                    VariableIds.Server_ServiceLevel
                };

                var result = await _opcUaClient.ReadAsync(commonNodes);
                return result.IsSuccess ? result.Data : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent values");
                return null;
            }
        }

        public async Task<OpcUaOperationResult<List<ApplicationDescription>>> FindServersAsync(string? discoveryUrl = null)
        {
            return await _opcUaClient.FindServersAsync(discoveryUrl);
        }

        private async void OnConnectionStatusChanged(object? sender, OpcUaConnectionEventArgs e)
        {
            _logger.LogInformation("Connection status changed: {IsConnected}", e.IsConnected);
            
            try
            {
                await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", e.IsConnected, e.Reason);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending connection status to clients");
            }
        }

        private async void OnDataChanged(object? sender, OpcUaNotificationEventArgs e)
        {
            try
            {
                var dataChanges = e.DataChanges.Select(dc => new
                {
                    NodeId = dc.NodeId.ToString(),
                    Value = dc.Value?.ToString(),
                    StatusCode = dc.StatusCode.ToString(),
                    SourceTimestamp = dc.SourceTimestamp,
                    ServerTimestamp = dc.ServerTimestamp,
                    DisplayName = dc.DisplayName
                }).ToList();

                await _hubContext.Clients.All.SendAsync("DataChanged", dataChanges);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending data changes to clients");
            }
        }

        private async void OnEventReceived(object? sender, OpcUaEventNotificationArgs e)
        {
            try
            {
                await _hubContext.Clients.All.SendAsync("EventReceived", new
                {
                    EventType = e.EventType.ToString(),
                    Message = e.Message,
                    Severity = e.Severity,
                    Time = e.Time,
                    Fields = e.EventFields.Select(f => new { f.Name, Value = f.Value?.ToString() })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending event to clients");
            }
        }

        private async void OnErrorOccurred(object? sender, OpcUaErrorEventArgs e)
        {
            _logger.LogError(e.Exception, "OPC UA error: {Message}", e.Message);
            
            try
            {
                await _hubContext.Clients.All.SendAsync("ErrorOccurred", new
                {
                    Message = e.Message,
                    StatusCode = e.StatusCode.ToString(),
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending error notification to clients");
            }
        }
    }
}
