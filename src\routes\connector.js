const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

// This will be injected by the main server
let smartConnector = null;

// Middleware to inject SmartConnector instance
router.use((req, res, next) => {
  if (!smartConnector) {
    smartConnector = req.app.get('smartConnector');
  }
  next();
});

/**
 * Get SmartConnector status
 */
router.get('/status', (req, res) => {
  try {
    const status = smartConnector.getStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to get connector status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get connector status',
      error: error.message
    });
  }
});

/**
 * Start SmartConnector service
 */
router.post('/start', async (req, res) => {
  try {
    if (smartConnector.isRunning) {
      return res.json({
        success: true,
        message: 'SmartConnector is already running'
      });
    }

    await smartConnector.start();
    res.json({
      success: true,
      message: 'SmartConnector started successfully'
    });
  } catch (error) {
    logger.error('Failed to start connector:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start connector',
      error: error.message
    });
  }
});

/**
 * Stop SmartConnector service
 */
router.post('/stop', async (req, res) => {
  try {
    if (!smartConnector.isRunning) {
      return res.json({
        success: true,
        message: 'SmartConnector is already stopped'
      });
    }

    await smartConnector.stop();
    res.json({
      success: true,
      message: 'SmartConnector stopped successfully'
    });
  } catch (error) {
    logger.error('Failed to stop connector:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop connector',
      error: error.message
    });
  }
});

/**
 * Restart SmartConnector service
 */
router.post('/restart', async (req, res) => {
  try {
    if (smartConnector.isRunning) {
      await smartConnector.stop();
    }
    await smartConnector.start();
    
    res.json({
      success: true,
      message: 'SmartConnector restarted successfully'
    });
  } catch (error) {
    logger.error('Failed to restart connector:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to restart connector',
      error: error.message
    });
  }
});

/**
 * Get all connector rules
 */
router.get('/rules', (req, res) => {
  try {
    const rules = smartConnector.getRules();
    res.json({
      success: true,
      data: rules
    });
  } catch (error) {
    logger.error('Failed to get rules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get rules',
      error: error.message
    });
  }
});

/**
 * Add a new connector rule
 */
router.post('/rules', (req, res) => {
  try {
    const { ruleId, rule } = req.body;

    if (!ruleId || !rule) {
      return res.status(400).json({
        success: false,
        message: 'ruleId and rule are required'
      });
    }

    // Validate rule structure
    if (!rule.type || !['tag-change', 'scheduled'].includes(rule.type)) {
      return res.status(400).json({
        success: false,
        message: 'rule.type must be either "tag-change" or "scheduled"'
      });
    }

    if (rule.type === 'tag-change' && !rule.tagId) {
      return res.status(400).json({
        success: false,
        message: 'rule.tagId is required for tag-change rules'
      });
    }

    if (rule.type === 'scheduled' && !rule.schedule) {
      return res.status(400).json({
        success: false,
        message: 'rule.schedule is required for scheduled rules'
      });
    }

    // Convert condition and action strings to functions if provided
    if (typeof rule.condition === 'string') {
      try {
        rule.condition = new Function('currentValue', 'previousValue', rule.condition);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: 'Invalid condition function',
          error: error.message
        });
      }
    }

    if (typeof rule.action === 'string') {
      try {
        rule.action = new Function('tagData', rule.action);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: 'Invalid action function',
          error: error.message
        });
      }
    }

    smartConnector.addRule(ruleId, rule);
    
    res.json({
      success: true,
      message: `Rule ${ruleId} added successfully`
    });
  } catch (error) {
    logger.error('Failed to add rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add rule',
      error: error.message
    });
  }
});

/**
 * Remove a connector rule
 */
router.delete('/rules/:ruleId', (req, res) => {
  try {
    const { ruleId } = req.params;
    
    smartConnector.removeRule(ruleId);
    
    res.json({
      success: true,
      message: `Rule ${ruleId} removed successfully`
    });
  } catch (error) {
    logger.error('Failed to remove rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove rule',
      error: error.message
    });
  }
});

/**
 * Test a specific rule action
 */
router.post('/rules/:ruleId/test', async (req, res) => {
  try {
    const { ruleId } = req.params;
    const { testData } = req.body;
    
    const rules = smartConnector.getRules();
    const rule = smartConnector.rules.get(ruleId);
    
    if (!rule) {
      return res.status(404).json({
        success: false,
        message: `Rule ${ruleId} not found`
      });
    }

    // Execute the rule action with test data
    const mockTagData = testData || {
      tagId: rule.tagId || 'test.tag',
      value: true,
      previousValue: false,
      timestamp: new Date()
    };

    await rule.action(mockTagData);
    
    res.json({
      success: true,
      message: `Rule ${ruleId} test executed successfully`
    });
  } catch (error) {
    logger.error('Failed to test rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test rule',
      error: error.message
    });
  }
});

/**
 * Get connector configuration
 */
router.get('/config', (req, res) => {
  try {
    const config = require('../config/config');
    
    // Return sanitized config (without passwords)
    const sanitizedConfig = {
      kepware: {
        host: config.kepware.host,
        port: config.kepware.port,
        username: config.kepware.username,
        useHttps: config.kepware.useHttps,
        apiVersion: config.kepware.apiVersion
      },
      ews: {
        url: config.ews.url,
        username: config.ews.username,
        domain: config.ews.domain,
        version: config.ews.version
      },
      connector: config.connector,
      logging: config.logging
    };
    
    res.json({
      success: true,
      data: sanitizedConfig
    });
  } catch (error) {
    logger.error('Failed to get config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get configuration',
      error: error.message
    });
  }
});

/**
 * Get connector logs
 */
router.get('/logs', (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const config = require('../config/config');
    
    const logFile = config.logging.file;
    const lines = parseInt(req.query.lines) || 100;
    
    if (!fs.existsSync(logFile)) {
      return res.json({
        success: true,
        data: [],
        message: 'Log file not found'
      });
    }
    
    const logContent = fs.readFileSync(logFile, 'utf8');
    const logLines = logContent.split('\n').filter(line => line.trim());
    const recentLines = logLines.slice(-lines);
    
    res.json({
      success: true,
      data: recentLines,
      totalLines: logLines.length
    });
  } catch (error) {
    logger.error('Failed to get logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get logs',
      error: error.message
    });
  }
});

module.exports = router;
