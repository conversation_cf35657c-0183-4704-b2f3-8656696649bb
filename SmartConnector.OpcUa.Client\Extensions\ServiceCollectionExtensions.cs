using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmartConnector.OpcUa.Client.Services;

namespace SmartConnector.OpcUa.Client.Extensions
{
    /// <summary>
    /// Service collection extensions for OPC UA client
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add OPC UA client services to the service collection
        /// </summary>
        public static IServiceCollection AddOpcUaClient(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure OPC UA client
            var clientConfig = new OpcUaClientConfiguration();
            configuration.GetSection("OpcUaClient").Bind(clientConfig);
            services.AddSingleton(clientConfig);

            // Configure OPC UA client service
            services.Configure<OpcUaClientServiceConfiguration>(
                configuration.GetSection("OpcUaClientService"));

            // Register OPC UA client
            services.AddSingleton<IOpcUaClient>(provider =>
            {
                var config = provider.GetRequiredService<OpcUaClientConfiguration>();
                var logger = provider.GetRequiredService<ILogger<OpcUaClient>>();
                return new OpcUaClient(config, logger);
            });

            // Register OPC UA client service
            services.AddHostedService<OpcUaClientService>();

            return services;
        }

        /// <summary>
        /// Add OPC UA client services with custom configuration
        /// </summary>
        public static IServiceCollection AddOpcUaClient(
            this IServiceCollection services,
            OpcUaClientConfiguration clientConfig,
            OpcUaClientServiceConfiguration? serviceConfig = null)
        {
            // Register configurations
            services.AddSingleton(clientConfig);
            
            if (serviceConfig != null)
            {
                services.AddSingleton(serviceConfig);
            }

            // Register OPC UA client
            services.AddSingleton<IOpcUaClient>(provider =>
            {
                var config = provider.GetRequiredService<OpcUaClientConfiguration>();
                var logger = provider.GetRequiredService<ILogger<OpcUaClient>>();
                return new OpcUaClient(config, logger);
            });

            // Register OPC UA client service
            services.AddHostedService<OpcUaClientService>();

            return services;
        }

        /// <summary>
        /// Add OPC UA client only (without background service)
        /// </summary>
        public static IServiceCollection AddOpcUaClientOnly(this IServiceCollection services, OpcUaClientConfiguration config)
        {
            services.AddSingleton(config);

            services.AddSingleton<IOpcUaClient>(provider =>
            {
                var configuration = provider.GetRequiredService<OpcUaClientConfiguration>();
                var logger = provider.GetRequiredService<ILogger<OpcUaClient>>();
                return new OpcUaClient(configuration, logger);
            });

            return services;
        }

        /// <summary>
        /// Add OPC UA client factory
        /// </summary>
        public static IServiceCollection AddOpcUaClientFactory(this IServiceCollection services)
        {
            services.AddSingleton<IOpcUaClientFactory, OpcUaClientFactory>();
            return services;
        }
    }

    /// <summary>
    /// OPC UA client factory interface
    /// </summary>
    public interface IOpcUaClientFactory
    {
        IOpcUaClient CreateClient(OpcUaClientConfiguration config);
        IOpcUaClient CreateClient(string endpointUrl, string applicationName = "SmartConnector OPC UA Client");
    }

    /// <summary>
    /// OPC UA client factory implementation
    /// </summary>
    public class OpcUaClientFactory : IOpcUaClientFactory
    {
        private readonly ILogger<OpcUaClient> _logger;

        public OpcUaClientFactory(ILogger<OpcUaClient> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Create OPC UA client with configuration
        /// </summary>
        public IOpcUaClient CreateClient(OpcUaClientConfiguration config)
        {
            return new OpcUaClient(config, _logger);
        }

        /// <summary>
        /// Create OPC UA client with endpoint URL
        /// </summary>
        public IOpcUaClient CreateClient(string endpointUrl, string applicationName = "SmartConnector OPC UA Client")
        {
            var config = new OpcUaClientConfiguration
            {
                EndpointUrl = endpointUrl,
                ApplicationName = applicationName,
                ApplicationUri = $"urn:{applicationName.Replace(" ", "")}",
                Security = new OpcUaSecurityConfiguration
                {
                    SecurityPolicy = Opc.Ua.SecurityPolicies.None,
                    MessageSecurityMode = Opc.Ua.MessageSecurityMode.None,
                    UserIdentityType = Opc.Ua.UserTokenType.Anonymous
                }
            };

            return new OpcUaClient(config, _logger);
        }
    }
}
