using Microsoft.AspNetCore.SignalR;
using SmartConnector.Kepware.WebUI.Hubs;
using SmartConnector.Kepware.WebUI.Models;

namespace SmartConnector.Kepware.WebUI.Services
{
    /// <summary>
    /// Service for managing Kepware data operations and real-time updates
    /// </summary>
    public class KepwareDataService
    {
        private readonly KepwareConnector _kepwareConnector;
        private readonly IHubContext<KepwareHub> _hubContext;
        private readonly ILogger<KepwareDataService> _logger;
        private readonly Timer? _pollingTimer;
        private readonly List<string> _subscribedTags = new();
        private bool _isRunning = false;

        public bool IsConnected { get; private set; } = false;
        public DateTime LastUpdateTime { get; private set; } = DateTime.MinValue;
        public int PollingIntervalMs { get; set; } = 1000; // Default 1 second

        public KepwareDataService(
            KepwareConnector kepwareConnector,
            IHubContext<KepwareHub> hubContext,
            ILogger<KepwareDataService> logger)
        {
            _kepwareConnector = kepwareConnector;
            _hubContext = hubContext;
            _logger = logger;
        }

        /// <summary>
        /// Start the Kepware data service
        /// </summary>
        public async Task StartAsync()
        {
            try
            {
                _logger.LogInformation("Starting Kepware Data Service...");

                // Test connection to Kepware
                var connectionResult = await _kepwareConnector.TestConnectionAsync();
                IsConnected = connectionResult.IsSuccess;

                if (IsConnected)
                {
                    _logger.LogInformation("Successfully connected to Kepware server");
                    
                    // Start polling timer for subscribed tags
                    if (_pollingTimer == null)
                    {
                        //_pollingTimer = new Timer(PollSubscribedTags, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(PollingIntervalMs));
                    }
                    
                    _isRunning = true;
                    
                    // Notify clients of connection status
                    await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", new
                    {
                        IsConnected = true,
                        Message = "Connected to Kepware server",
                        Timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    _logger.LogWarning("Failed to connect to Kepware server: {Message}", connectionResult.Message);
                    
                    await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", new
                    {
                        IsConnected = false,
                        Message = connectionResult.Message,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting Kepware Data Service");
                IsConnected = false;
            }
        }

        /// <summary>
        /// Stop the Kepware data service
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                _logger.LogInformation("Stopping Kepware Data Service...");
                
                _isRunning = false;
                _pollingTimer?.Dispose();
                IsConnected = false;
                
                await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", new
                {
                    IsConnected = false,
                    Message = "Disconnected from Kepware server",
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping Kepware Data Service");
            }
        }

        /// <summary>
        /// Read specific tags from Kepware
        /// </summary>
        public async Task<KepwareOperationResult<List<KepwareTagValue>>> ReadTagsAsync(List<string> tagIds)
        {
            try
            {
                if (!IsConnected)
                {
                    return new KepwareOperationResult<List<KepwareTagValue>>
                    {
                        IsSuccess = false,
                        Message = "Not connected to Kepware server"
                    };
                }

                var result = await _kepwareConnector.ReadTagsAsync(tagIds);
                
                if (result.IsSuccess && result.Data != null)
                {
                    LastUpdateTime = DateTime.UtcNow;
                    
                    // Send real-time updates to clients
                    await _hubContext.Clients.All.SendAsync("TagValuesUpdated", result.Data);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading tags from Kepware");
                return new KepwareOperationResult<List<KepwareTagValue>>
                {
                    IsSuccess = false,
                    Message = "Error reading tags",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Write values to Kepware tags
        /// </summary>
        public async Task<KepwareOperationResult> WriteTagsAsync(List<KepwareTagWrite> tagWrites)
        {
            try
            {
                if (!IsConnected)
                {
                    return new KepwareOperationResult
                    {
                        IsSuccess = false,
                        Message = "Not connected to Kepware server"
                    };
                }

                var result = await _kepwareConnector.WriteTagsAsync(tagWrites);
                
                if (result.IsSuccess)
                {
                    // Notify clients of successful write
                    await _hubContext.Clients.All.SendAsync("TagsWritten", new
                    {
                        TagIds = tagWrites.Select(tw => tw.TagId).ToList(),
                        Timestamp = DateTime.UtcNow,
                        Message = "Tags written successfully"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing tags to Kepware");
                return new KepwareOperationResult
                {
                    IsSuccess = false,
                    Message = "Error writing tags",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Subscribe to tag updates
        /// </summary>
        public async Task<bool> SubscribeToTagsAsync(List<string> tagIds)
        {
            try
            {
                foreach (var tagId in tagIds)
                {
                    if (!_subscribedTags.Contains(tagId))
                    {
                        _subscribedTags.Add(tagId);
                    }
                }

                _logger.LogInformation("Subscribed to {Count} tags", tagIds.Count);
                
                // Immediately read the subscribed tags
                if (_subscribedTags.Any())
                {
                    await ReadTagsAsync(_subscribedTags);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing to tags");
                return false;
            }
        }

        /// <summary>
        /// Unsubscribe from tag updates
        /// </summary>
        public async Task<bool> UnsubscribeFromTagsAsync(List<string> tagIds)
        {
            try
            {
                foreach (var tagId in tagIds)
                {
                    _subscribedTags.Remove(tagId);
                }

                _logger.LogInformation("Unsubscribed from {Count} tags", tagIds.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unsubscribing from tags");
                return false;
            }
        }

        /// <summary>
        /// Get Kepware server status
        /// </summary>
        public async Task<KepwareOperationResult<KepwareServerStatus>> GetServerStatusAsync()
        {
            try
            {
                return await _kepwareConnector.GetServerStatusAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server status");
                return new KepwareOperationResult<KepwareServerStatus>
                {
                    IsSuccess = false,
                    Message = "Error getting server status",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Poll subscribed tags for updates
        /// </summary>
        private async void PollSubscribedTags(object? state)
        {
            if (!_isRunning || !IsConnected || !_subscribedTags.Any())
                return;

            try
            {
                await ReadTagsAsync(_subscribedTags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error polling subscribed tags");
            }
        }

        /// <summary>
        /// Get current subscribed tags
        /// </summary>
        public List<string> GetSubscribedTags()
        {
            return new List<string>(_subscribedTags);
        }
    }
}
