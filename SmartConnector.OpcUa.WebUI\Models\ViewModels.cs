using SmartConnector.OpcUa.Client;

namespace SmartConnector.OpcUa.WebUI.Models
{
    public class DashboardViewModel
    {
        public bool IsConnected { get; set; }
        public string ConnectionStatus { get; set; } = "Disconnected";
        public string ServerUrl { get; set; } = string.Empty;
        public DateTime LastUpdate { get; set; }
        public OpcUaServerInfo? ServerInfo { get; set; }
        public List<OpcUaValue>? RecentValues { get; set; }
    }

    public class SettingsViewModel
    {
        public string EndpointUrl { get; set; } = "opc.tcp://localhost:4840";
        public bool IsConnected { get; set; }
        public bool AutoReconnect { get; set; } = true;
        public string SecurityPolicy { get; set; } = "None";
        public string MessageSecurityMode { get; set; } = "None";
        public string UserIdentityType { get; set; } = "Anonymous";
        public string? Username { get; set; }
        public string? Password { get; set; }
        public int ConnectionTimeout { get; set; } = 30000;
        public int SessionTimeout { get; set; } = 60000;
        public int KeepAliveInterval { get; set; } = 5000;
    }

    public class MonitorViewModel
    {
        public List<MonitoredItemViewModel> MonitoredItems { get; set; } = new();
        public bool IsConnected { get; set; }
        public DateTime LastUpdate { get; set; }
    }

    public class MonitoredItemViewModel
    {
        public string NodeId { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string StatusCode { get; set; } = string.Empty;
        public DateTime SourceTimestamp { get; set; }
        public DateTime ServerTimestamp { get; set; }
        public double SamplingInterval { get; set; } = 1000;
        public bool IsSubscribed { get; set; }
    }

    public class ErrorViewModel
    {
        public string? RequestId { get; set; }
        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
    }
}
