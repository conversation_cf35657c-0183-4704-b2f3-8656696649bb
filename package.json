{"name": "smartconnector-ews-kepware", "version": "1.0.0", "description": "SmartConnector EWS webserver for Kepware REST API integration", "main": "src/server.js", "scripts": {"start": "node start.js", "dev": "nodemon start.js", "server": "node src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["smartconnector", "ews", "kepware", "rest-api", "webserver", "exchange"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "cron": "^3.1.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "node-ews": "^3.5.0", "ntlm-client": "^0.1.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}