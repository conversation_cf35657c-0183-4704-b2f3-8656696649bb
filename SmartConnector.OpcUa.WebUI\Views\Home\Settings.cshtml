@{
    ViewData["Title"] = "Settings";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-cog"></i> Settings</h1>
</div>

<!-- Service Mode Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-toggle-on"></i> Service Mode</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Current Mode:</strong> <span id="currentMode">Loading...</span>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-play-circle"></i> Demo Mode (Mock Service)</h6>
                            </div>
                            <div class="card-body">
                                <p>Uses simulated OPC UA data for demonstration purposes.</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> No real server required</li>
                                    <li><i class="fas fa-check text-success"></i> Instant setup</li>
                                    <li><i class="fas fa-check text-success"></i> Simulated industrial data</li>
                                    <li><i class="fas fa-check text-success"></i> Perfect for testing UI</li>
                                </ul>
                                <button class="btn btn-primary" onclick="switchToMockService()">
                                    <i class="fas fa-toggle-on"></i> Use Demo Mode
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-server"></i> Real Server Mode</h6>
                            </div>
                            <div class="card-body">
                                <p>Connects to actual OPC UA servers for real data.</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Real industrial data</li>
                                    <li><i class="fas fa-check text-success"></i> Full OPC UA features</li>
                                    <li><i class="fas fa-check text-success"></i> Production ready</li>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> Requires OPC UA server</li>
                                </ul>
                                <button class="btn btn-success" onclick="switchToRealService()">
                                    <i class="fas fa-toggle-on"></i> Use Real Server Mode
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Server Connection Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-network-wired"></i> Server Connection</h5>
            </div>
            <div class="card-body">
                <!-- Quick Connect to Demo Servers -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6><i class="fas fa-rocket"></i> Quick Connect to Demo Servers</h6>
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h6>Prosys Simulation Server</h6>
                                        <small class="text-muted">opcua.demo-this.com:51210</small><br>
                                        <button class="btn btn-info btn-sm mt-2" onclick="connectToServer('opc.tcp://opcua.demo-this.com:51210/UA/SampleServer')">
                                            <i class="fas fa-plug"></i> Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <h6>Unified Automation Demo</h6>
                                        <small class="text-muted">opcuaserver.com:48010</small><br>
                                        <button class="btn btn-warning btn-sm mt-2" onclick="connectToServer('opc.tcp://opcuaserver.com:48010')">
                                            <i class="fas fa-plug"></i> Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <h6>Local Server</h6>
                                        <small class="text-muted">localhost:4840</small><br>
                                        <button class="btn btn-secondary btn-sm mt-2" onclick="connectToServer('opc.tcp://localhost:4840')">
                                            <i class="fas fa-plug"></i> Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Server Connection -->
                <div class="row">
                    <div class="col-md-8">
                        <label for="serverEndpoint" class="form-label">Server Endpoint URL</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-server"></i></span>
                            <input type="text" class="form-control" id="serverEndpoint" 
                                   placeholder="opc.tcp://your-server:4840" 
                                   value="opc.tcp://localhost:4840">
                            <button class="btn btn-primary" onclick="connectToCustomServer()">
                                <i class="fas fa-plug"></i> Connect
                            </button>
                        </div>
                        <small class="form-text text-muted">Enter the OPC UA server endpoint URL</small>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Connection Status</label>
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <div id="connectionStatus">
                                    <i class="fas fa-circle text-secondary"></i>
                                    <span>Not Connected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Security Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-shield-alt"></i> Security Settings</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="securityPolicy" class="form-label">Security Policy</label>
                        <select class="form-select" id="securityPolicy">
                            <option value="None" selected>None</option>
                            <option value="Basic128Rsa15">Basic128Rsa15</option>
                            <option value="Basic256">Basic256</option>
                            <option value="Basic256Sha256">Basic256Sha256</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="messageSecurityMode" class="form-label">Message Security Mode</label>
                        <select class="form-select" id="messageSecurityMode">
                            <option value="None" selected>None</option>
                            <option value="Sign">Sign</option>
                            <option value="SignAndEncrypt">Sign and Encrypt</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="userIdentityType" class="form-label">User Identity</label>
                        <select class="form-select" id="userIdentityType">
                            <option value="Anonymous" selected>Anonymous</option>
                            <option value="UserName">Username/Password</option>
                            <option value="Certificate">Certificate</option>
                        </select>
                    </div>
                </div>
                
                <!-- Username/Password Section (Hidden by default) -->
                <div class="row mt-3" id="credentialsSection" style="display: none;">
                    <div class="col-md-6">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" placeholder="Enter username">
                    </div>
                    <div class="col-md-6">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" placeholder="Enter password">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Connection Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-clipboard-list"></i> Connection Results</h5>
            </div>
            <div class="card-body">
                <div id="connectionResults">
                    <p class="text-muted mb-0">No connection attempts yet. Configure settings above and click Connect.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Load current settings on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadCurrentSettings();
        updateConnectionStatus();
        
        // Show/hide credentials section based on user identity type
        document.getElementById('userIdentityType').addEventListener('change', function() {
            const credentialsSection = document.getElementById('credentialsSection');
            if (this.value === 'UserName') {
                credentialsSection.style.display = 'block';
            } else {
                credentialsSection.style.display = 'none';
            }
        });
    });

    function loadCurrentSettings() {
        // This would typically load from server configuration
        document.getElementById('currentMode').textContent = 'Demo Mode (Mock Service)';
    }

    function updateConnectionStatus() {
        fetch('/api/opcua/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.IsConnected) {
                    statusDiv.innerHTML = `
                        <i class="fas fa-circle text-success"></i>
                        <span>Connected</span><br>
                        <small class="text-muted">${data.ServerUrl}</small>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <i class="fas fa-circle text-secondary"></i>
                        <span>Not Connected</span>
                    `;
                }
            })
            .catch(error => {
                console.error('Error checking connection status:', error);
            });
    }

    function switchToMockService() {
        showConnectionResult('info', 'Switching to Demo Mode', 'The application will use simulated OPC UA data. Restart the application to apply changes.');
        document.getElementById('currentMode').textContent = 'Demo Mode (Mock Service)';
    }

    function switchToRealService() {
        showConnectionResult('warning', 'Switching to Real Server Mode', 'The application will connect to real OPC UA servers. Restart the application to apply changes.');
        document.getElementById('currentMode').textContent = 'Real Server Mode';
    }

    function connectToServer(endpoint) {
        document.getElementById('serverEndpoint').value = endpoint;
        connectToCustomServer();
    }

    function connectToCustomServer() {
        const endpoint = document.getElementById('serverEndpoint').value.trim();
        if (!endpoint) {
            alert('Please enter a server endpoint URL');
            return;
        }

        showConnectionInProgress('Connecting to ' + endpoint + '...');

        fetch('/api/opcua/connect', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                endpointUrl: endpoint
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.Success) {
                showConnectionResult('success', 'Connection Successful', `Successfully connected to ${endpoint}`);
                updateConnectionStatus();
            } else {
                showConnectionResult('danger', 'Connection Failed', data.Message || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showConnectionResult('danger', 'Connection Error', 'Network error: ' + error.message);
        });
    }

    function showConnectionInProgress(message) {
        const resultsDiv = document.getElementById('connectionResults');
        resultsDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span>${message}</span>
            </div>
        `;
    }

    function showConnectionResult(type, title, message) {
        const resultsDiv = document.getElementById('connectionResults');
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'danger' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'danger' ? 'fa-exclamation-triangle' : 
                    type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

        resultsDiv.innerHTML = `
            <div class="alert ${alertClass}">
                <h6><i class="fas ${icon}"></i> ${title}</h6>
                <p class="mb-0">${message}</p>
                <small class="text-muted">Timestamp: ${new Date().toLocaleString()}</small>
            </div>
        `;
    }
</script>
