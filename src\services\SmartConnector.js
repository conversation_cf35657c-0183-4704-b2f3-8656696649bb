const EventEmitter = require('events');
const cron = require('cron');
const KepwareClient = require('../clients/KepwareClient');
const EWSClient = require('../clients/EWSClient');
const config = require('../config/config');
const logger = require('../utils/logger');

class SmartConnector extends EventEmitter {
  constructor() {
    super();
    this.kepwareClient = new KepwareClient(config.kepware);
    this.ewsClient = new EWSClient(config.ews);
    this.isRunning = false;
    this.pollInterval = null;
    this.rules = new Map();
    this.tagSubscriptions = new Map();
    this.lastTagValues = new Map();
    this.cronJobs = new Map();
    
    // Initialize default rules
    this.initializeDefaultRules();
  }

  /**
   * Start the SmartConnector service
   */
  async start() {
    try {
      logger.info('Starting SmartConnector service...');
      
      // Test connections
      const kepwareTest = await this.kepwareClient.testConnection();
      const ewsTest = await this.ewsClient.testConnection();
      
      if (!kepwareTest.success) {
        throw new Error(`Kepware connection failed: ${kepwareTest.message}`);
      }
      
      if (!ewsTest.success) {
        throw new Error(`EWS connection failed: ${ewsTest.message}`);
      }
      
      logger.info('Both Kepware and EWS connections established successfully');
      
      // Start polling for tag changes
      this.startPolling();
      
      // Start scheduled jobs
      this.startScheduledJobs();
      
      this.isRunning = true;
      this.emit('started');
      
      logger.info('SmartConnector service started successfully');
    } catch (error) {
      logger.error('Failed to start SmartConnector service:', error);
      throw error;
    }
  }

  /**
   * Stop the SmartConnector service
   */
  async stop() {
    try {
      logger.info('Stopping SmartConnector service...');
      
      this.isRunning = false;
      
      // Stop polling
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
        this.pollInterval = null;
      }
      
      // Stop all cron jobs
      this.cronJobs.forEach(job => job.stop());
      this.cronJobs.clear();
      
      // Clear subscriptions
      this.tagSubscriptions.clear();
      
      this.emit('stopped');
      logger.info('SmartConnector service stopped successfully');
    } catch (error) {
      logger.error('Error stopping SmartConnector service:', error);
      throw error;
    }
  }

  /**
   * Initialize default connector rules
   */
  initializeDefaultRules() {
    // Example rule: Send email when alarm tag goes high
    this.addRule('alarm-notification', {
      type: 'tag-change',
      tagId: 'Channel1.Device1.AlarmStatus',
      condition: (currentValue, previousValue) => {
        return currentValue === true && previousValue === false;
      },
      action: async (tagData) => {
        await this.ewsClient.sendEmail({
          to: ['<EMAIL>'],
          subject: 'ALARM: Equipment Alert',
          body: `Alarm triggered at ${new Date().toISOString()}\nTag: ${tagData.tagId}\nValue: ${tagData.value}`,
          isHtml: false
        });
      }
    });

    // Example rule: Create calendar appointment for maintenance
    this.addRule('maintenance-schedule', {
      type: 'tag-change',
      tagId: 'Channel1.Device1.MaintenanceRequired',
      condition: (currentValue, previousValue) => {
        return currentValue === true && previousValue === false;
      },
      action: async (tagData) => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(9, 0, 0, 0);
        
        const endTime = new Date(tomorrow);
        endTime.setHours(10, 0, 0, 0);
        
        await this.ewsClient.createAppointment({
          subject: 'Equipment Maintenance Required',
          body: `Maintenance required for equipment.\nTag: ${tagData.tagId}\nTriggered: ${new Date().toISOString()}`,
          start: tomorrow,
          end: endTime,
          location: 'Production Floor',
          attendees: ['<EMAIL>', '<EMAIL>']
        });
      }
    });

    // Example rule: Daily production report
    this.addRule('daily-report', {
      type: 'scheduled',
      schedule: '0 17 * * 1-5', // 5 PM on weekdays
      action: async () => {
        const productionTags = [
          'Channel1.Device1.ProductionCount',
          'Channel1.Device1.EfficiencyPercent',
          'Channel1.Device1.DowntimeMinutes'
        ];
        
        const tagData = await this.kepwareClient.readTags(productionTags);
        const report = this.generateProductionReport(tagData);
        
        await this.ewsClient.sendEmail({
          to: ['<EMAIL>', '<EMAIL>'],
          subject: `Daily Production Report - ${new Date().toDateString()}`,
          body: report,
          isHtml: true
        });
      }
    });
  }

  /**
   * Add a new connector rule
   */
  addRule(ruleId, rule) {
    this.rules.set(ruleId, rule);
    
    if (rule.type === 'scheduled') {
      this.addScheduledJob(ruleId, rule);
    } else if (rule.type === 'tag-change') {
      this.addTagSubscription(rule.tagId);
    }
    
    logger.info(`Added connector rule: ${ruleId}`);
  }

  /**
   * Remove a connector rule
   */
  removeRule(ruleId) {
    const rule = this.rules.get(ruleId);
    if (rule) {
      if (rule.type === 'scheduled' && this.cronJobs.has(ruleId)) {
        this.cronJobs.get(ruleId).stop();
        this.cronJobs.delete(ruleId);
      }
      
      this.rules.delete(ruleId);
      logger.info(`Removed connector rule: ${ruleId}`);
    }
  }

  /**
   * Add tag subscription for monitoring
   */
  addTagSubscription(tagId) {
    if (!this.tagSubscriptions.has(tagId)) {
      this.tagSubscriptions.set(tagId, true);
      logger.debug(`Added tag subscription: ${tagId}`);
    }
  }

  /**
   * Add scheduled job
   */
  addScheduledJob(ruleId, rule) {
    try {
      const job = new cron.CronJob(rule.schedule, async () => {
        try {
          logger.debug(`Executing scheduled rule: ${ruleId}`);
          await rule.action();
        } catch (error) {
          logger.error(`Error executing scheduled rule ${ruleId}:`, error);
        }
      });
      
      this.cronJobs.set(ruleId, job);
      
      if (this.isRunning) {
        job.start();
      }
    } catch (error) {
      logger.error(`Failed to create scheduled job for rule ${ruleId}:`, error);
    }
  }

  /**
   * Start polling for tag changes
   */
  startPolling() {
    this.pollInterval = setInterval(async () => {
      try {
        await this.pollTagChanges();
      } catch (error) {
        logger.error('Error during tag polling:', error);
      }
    }, config.connector.pollInterval);
  }

  /**
   * Start all scheduled jobs
   */
  startScheduledJobs() {
    this.cronJobs.forEach(job => job.start());
  }

  /**
   * Poll for tag changes and execute rules
   */
  async pollTagChanges() {
    if (this.tagSubscriptions.size === 0) {
      return;
    }

    try {
      const tagIds = Array.from(this.tagSubscriptions.keys());
      const tagData = await this.kepwareClient.readTags(tagIds);
      
      if (tagData && tagData.readResults) {
        for (const result of tagData.readResults) {
          await this.processTagChange(result);
        }
      }
    } catch (error) {
      logger.error('Error polling tag changes:', error);
    }
  }

  /**
   * Process individual tag change
   */
  async processTagChange(tagResult) {
    const { id: tagId, v: value, s: status } = tagResult;
    
    if (status !== 'GOOD') {
      logger.warn(`Tag ${tagId} has bad quality: ${status}`);
      return;
    }

    const previousValue = this.lastTagValues.get(tagId);
    this.lastTagValues.set(tagId, value);

    // Check rules for this tag
    for (const [ruleId, rule] of this.rules) {
      if (rule.type === 'tag-change' && rule.tagId === tagId) {
        try {
          if (rule.condition(value, previousValue)) {
            logger.info(`Rule condition met for ${ruleId}, executing action`);
            await rule.action({ tagId, value, previousValue, timestamp: new Date() });
          }
        } catch (error) {
          logger.error(`Error executing rule ${ruleId}:`, error);
        }
      }
    }
  }

  /**
   * Generate production report
   */
  generateProductionReport(tagData) {
    let report = '<h2>Daily Production Report</h2>';
    report += `<p><strong>Date:</strong> ${new Date().toDateString()}</p>`;
    report += '<table border="1" style="border-collapse: collapse;">';
    report += '<tr><th>Metric</th><th>Value</th></tr>';
    
    if (tagData && tagData.readResults) {
      for (const result of tagData.readResults) {
        const metricName = result.id.split('.').pop();
        report += `<tr><td>${metricName}</td><td>${result.v}</td></tr>`;
      }
    }
    
    report += '</table>';
    return report;
  }

  /**
   * Get connector status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      rulesCount: this.rules.size,
      tagSubscriptions: this.tagSubscriptions.size,
      scheduledJobs: this.cronJobs.size,
      lastPoll: new Date().toISOString()
    };
  }

  /**
   * Get all rules
   */
  getRules() {
    const rules = {};
    for (const [ruleId, rule] of this.rules) {
      rules[ruleId] = {
        type: rule.type,
        tagId: rule.tagId,
        schedule: rule.schedule
      };
    }
    return rules;
  }
}

module.exports = SmartConnector;
