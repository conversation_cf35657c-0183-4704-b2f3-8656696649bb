using Microsoft.Exchange.WebServices.Data;

namespace SmartConnector.Ews.Server
{
    /// <summary>
    /// Custom trace listener for EWS debugging
    /// </summary>
    public class EwsTraceListener : ITraceListener
    {
        private readonly string _traceFilePath;
        private readonly object _lockObject = new object();

        public EwsTraceListener(string traceFilePath)
        {
            _traceFilePath = traceFilePath ?? throw new ArgumentNullException(nameof(traceFilePath));
            
            // Ensure directory exists
            var directory = Path.GetDirectoryName(_traceFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }

        /// <summary>
        /// Trace EWS operations
        /// </summary>
        public void Trace(string traceType, string traceMessage)
        {
            lock (_lockObject)
            {
                try
                {
                    var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var logEntry = $"[{timestamp}] [{traceType}] {traceMessage}{Environment.NewLine}";
                    
                    File.AppendAllText(_traceFilePath, logEntry);
                }
                catch (Exception ex)
                {
                    // Avoid throwing exceptions from trace listener
                    Console.WriteLine($"Failed to write EWS trace: {ex.Message}");
                }
            }
        }
    }
}
