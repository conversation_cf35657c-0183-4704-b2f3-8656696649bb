using Microsoft.Exchange.WebServices.Data;

namespace SmartConnector.Ews.Server
{
    /// <summary>
    /// EWS email message model
    /// </summary>
    public class EwsEmailMessage
    {
        public string? MessageId { get; set; }
        public List<string> To { get; set; } = new();
        public List<string> Cc { get; set; } = new();
        public List<string> Bcc { get; set; } = new();
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = false;
        public string From { get; set; } = string.Empty;
        public DateTime? ReceivedTime { get; set; }
        public DateTime? SentTime { get; set; }
        public bool IsRead { get; set; }
        public EwsImportance Priority { get; set; } = EwsImportance.Normal;
        public List<EwsAttachment> Attachments { get; set; } = new();
    }

    /// <summary>
    /// EWS appointment model
    /// </summary>
    public class EwsAppointment
    {
        public string? AppointmentId { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public string Location { get; set; } = string.Empty;
        public List<string> Attendees { get; set; } = new();
        public List<string> RequiredAttendees { get; set; } = new();
        public List<string> OptionalAttendees { get; set; } = new();
        public bool IsAllDay { get; set; }
        public bool IsRecurring { get; set; }
        public string Organizer { get; set; } = string.Empty;
        public EwsImportance Priority { get; set; } = EwsImportance.Normal;
        public int ReminderMinutes { get; set; } = 15;
        public EwsFreeBusyStatus FreeBusyStatus { get; set; } = EwsFreeBusyStatus.Busy;
    }

    /// <summary>
    /// EWS task model
    /// </summary>
    public class EwsTask
    {
        public string? TaskId { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public EwsImportance Priority { get; set; } = EwsImportance.Normal;
        public EwsTaskStatus Status { get; set; } = EwsTaskStatus.NotStarted;
        public double PercentComplete { get; set; } = 0;
        public string Owner { get; set; } = string.Empty;
        public List<string> Categories { get; set; } = new();
        public int ReminderMinutes { get; set; } = 0;
        public bool IsReminderSet { get; set; } = false;
    }

    /// <summary>
    /// EWS attachment model
    /// </summary>
    public class EwsAttachment
    {
        public string Name { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public byte[]? Content { get; set; }
        public long Size { get; set; }
        public bool IsInline { get; set; }
        public string? ContentId { get; set; }
    }

    /// <summary>
    /// EWS configuration model
    /// </summary>
    public class EwsConfiguration
    {
        public string ExchangeUrl { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string? Domain { get; set; }
        public ExchangeVersion ExchangeVersion { get; set; } = ExchangeVersion.Exchange2016;
        public bool UseDefaultCredentials { get; set; } = false;
        public bool EnableTracing { get; set; } = false;
        public string? TraceFilePath { get; set; }
        public int TimeoutMinutes { get; set; } = 5;
        public bool AcceptInvalidCertificates { get; set; } = false;
        public string? ProxyUrl { get; set; }
        public string? ProxyUsername { get; set; }
        public string? ProxyPassword { get; set; }
    }

    /// <summary>
    /// EWS folder types
    /// </summary>
    public enum EwsFolderType
    {
        Inbox,
        SentItems,
        DeletedItems,
        Drafts,
        Calendar,
        Contacts,
        Tasks,
        Notes,
        JunkEmail,
        Outbox
    }

    /// <summary>
    /// EWS search filter
    /// </summary>
    public class EwsSearchFilter
    {
        public string? Subject { get; set; }
        public string? From { get; set; }
        public string? Body { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsRead { get; set; }
        public EwsImportance? Priority { get; set; }
        public List<string> Categories { get; set; } = new();
        public int MaxResults { get; set; } = 50;
    }

    /// <summary>
    /// EWS importance levels
    /// </summary>
    public enum EwsImportance
    {
        Low = 0,
        Normal = 1,
        High = 2
    }

    /// <summary>
    /// EWS task status
    /// </summary>
    public enum EwsTaskStatus
    {
        NotStarted = 0,
        InProgress = 1,
        Completed = 2,
        WaitingOnOthers = 3,
        Deferred = 4
    }

    /// <summary>
    /// EWS free/busy status
    /// </summary>
    public enum EwsFreeBusyStatus
    {
        Free = 0,
        Tentative = 1,
        Busy = 2,
        OutOfOffice = 3,
        WorkingElsewhere = 4
    }
}
