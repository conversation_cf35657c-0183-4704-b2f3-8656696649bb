@{
    ViewData["Title"] = "Node Browser";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-sitemap"></i> Node Browser</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="expandAll()">
                <i class="fas fa-expand-arrows-alt"></i> Expand All
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                <i class="fas fa-compress-arrows-alt"></i> Collapse All
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Node Tree -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-tree"></i> Address Space</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="searchNodes" placeholder="Search nodes..." onkeyup="filterNodes()">
                </div>
                <div id="nodeTree" class="node-tree">
                    <div class="text-center py-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading address space...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Node Details -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-info-circle"></i> Node Details</h5>
            </div>
            <div class="card-body">
                <div id="nodeDetails">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-mouse-pointer fa-2x mb-3"></i>
                        <p>Select a node from the tree to view details</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Node Operations -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-tools"></i> Node Operations</h5>
            </div>
            <div class="card-body">
                <div id="nodeOperations" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="readNodeValue()">
                                <i class="fas fa-download"></i> Read Value
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-success w-100 mb-2" onclick="writeNodeValue()">
                                <i class="fas fa-upload"></i> Write Value
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-info w-100 mb-2" onclick="subscribeToNode()">
                                <i class="fas fa-bell"></i> Subscribe
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-warning w-100 mb-2" onclick="browseNodeReferences()">
                                <i class="fas fa-project-diagram"></i> References
                            </button>
                        </div>
                    </div>
                    
                    <!-- Write Value Form -->
                    <div id="writeValueForm" style="display: none;" class="mt-3">
                        <div class="border rounded p-3 bg-light">
                            <h6>Write Value</h6>
                            <div class="mb-2">
                                <label class="form-label">Value:</label>
                                <input type="text" class="form-control" id="writeValue" placeholder="Enter value to write">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Data Type:</label>
                                <select class="form-select" id="writeDataType">
                                    <option value="string">String</option>
                                    <option value="number">Number</option>
                                    <option value="boolean">Boolean</option>
                                    <option value="datetime">DateTime</option>
                                </select>
                            </div>
                            <button class="btn btn-success btn-sm" onclick="executeWrite()">
                                <i class="fas fa-check"></i> Write
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="cancelWrite()">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>
                </div>
                <div id="noNodeSelected" class="text-center text-muted py-3">
                    <p>Select a variable node to perform operations</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Value Display Modal -->
<div class="modal fade" id="valueModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Node Value</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="valueContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    let selectedNodeId = null;
    let nodeCache = {};

    // Load root nodes on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadNodes();
    });

    function loadNodes(parentNodeId = null) {
        const url = parentNodeId ? `/api/opcua/browse?nodeId=${encodeURIComponent(parentNodeId)}` : '/api/opcua/browse';
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (!parentNodeId) {
                        // Root nodes
                        displayNodes(data.data, document.getElementById('nodeTree'));
                    } else {
                        // Child nodes - update the parent's children
                        const parentElement = document.querySelector(`[data-node-id="${parentNodeId}"]`);
                        if (parentElement) {
                            const childContainer = parentElement.nextElementSibling;
                            if (childContainer && childContainer.classList.contains('node-children')) {
                                displayNodes(data.data, childContainer);
                            }
                        }
                    }
                    nodeCache[parentNodeId || 'root'] = data.data;
                } else {
                    console.error('Failed to load nodes:', data.message);
                    if (!parentNodeId) {
                        document.getElementById('nodeTree').innerHTML = 
                            '<div class="alert alert-danger">Failed to load nodes: ' + data.message + '</div>';
                    }
                }
            })
            .catch(error => {
                console.error('Error loading nodes:', error);
                if (!parentNodeId) {
                    document.getElementById('nodeTree').innerHTML = 
                        '<div class="alert alert-danger">Error loading nodes: ' + error.message + '</div>';
                }
            });
    }

    function displayNodes(nodes, container) {
        container.innerHTML = '';
        
        nodes.forEach(node => {
            const nodeElement = createNodeElement(node);
            container.appendChild(nodeElement);
        });
    }

    function createNodeElement(node) {
        const nodeDiv = document.createElement('div');
        nodeDiv.className = 'node-item';
        nodeDiv.setAttribute('data-node-id', node.nodeId);
        
        const icon = getNodeIcon(node.nodeClass);
        const hasChildren = node.hasChildren;
        
        nodeDiv.innerHTML = `
            <div class="d-flex align-items-center">
                ${hasChildren ? '<i class="fas fa-chevron-right me-1 expand-icon" onclick="toggleNode(this)"></i>' : '<span class="me-3"></span>'}
                <i class="${icon} me-2"></i>
                <span class="node-name" onclick="selectNode('${node.nodeId}')">${node.displayName}</span>
                <small class="text-muted ms-2">(${node.nodeClass})</small>
            </div>
        `;
        
        if (hasChildren) {
            const childrenDiv = document.createElement('div');
            childrenDiv.className = 'node-children ms-4';
            childrenDiv.style.display = 'none';
            nodeDiv.appendChild(childrenDiv);
        }
        
        return nodeDiv;
    }

    function getNodeIcon(nodeClass) {
        switch (nodeClass) {
            case 'Object': return 'fas fa-cube text-primary';
            case 'Variable': return 'fas fa-tag text-success';
            case 'Method': return 'fas fa-cog text-warning';
            case 'ObjectType': return 'fas fa-shapes text-info';
            case 'VariableType': return 'fas fa-tags text-secondary';
            case 'DataType': return 'fas fa-database text-dark';
            case 'ReferenceType': return 'fas fa-link text-muted';
            case 'View': return 'fas fa-eye text-purple';
            default: return 'fas fa-question text-muted';
        }
    }

    function toggleNode(element) {
        const nodeItem = element.closest('.node-item');
        const childrenDiv = nodeItem.querySelector('.node-children');
        const nodeId = nodeItem.getAttribute('data-node-id');
        
        if (childrenDiv.style.display === 'none') {
            // Expand
            element.classList.remove('fa-chevron-right');
            element.classList.add('fa-chevron-down');
            childrenDiv.style.display = 'block';
            
            // Load children if not already loaded
            if (childrenDiv.children.length === 0) {
                childrenDiv.innerHTML = '<div class="text-center py-2"><div class="spinner-border spinner-border-sm"></div></div>';
                loadNodes(nodeId);
            }
        } else {
            // Collapse
            element.classList.remove('fa-chevron-down');
            element.classList.add('fa-chevron-right');
            childrenDiv.style.display = 'none';
        }
    }

    function selectNode(nodeId) {
        selectedNodeId = nodeId;
        
        // Update selection visual
        document.querySelectorAll('.node-item').forEach(item => {
            item.classList.remove('bg-primary', 'text-white');
        });
        
        const selectedElement = document.querySelector(`[data-node-id="${nodeId}"]`);
        if (selectedElement) {
            selectedElement.classList.add('bg-primary', 'text-white');
        }
        
        // Load node details
        loadNodeDetails(nodeId);
        
        // Show operations panel
        document.getElementById('nodeOperations').style.display = 'block';
        document.getElementById('noNodeSelected').style.display = 'none';
    }

    function loadNodeDetails(nodeId) {
        // For now, show basic info. In a full implementation, you'd call an API to get detailed attributes
        const detailsDiv = document.getElementById('nodeDetails');
        detailsDiv.innerHTML = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <tr><td><strong>Node ID:</strong></td><td><code>${nodeId}</code></td></tr>
                    <tr><td><strong>Selected:</strong></td><td>${new Date().toLocaleTimeString()}</td></tr>
                </table>
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Detailed node attributes would be loaded here in a full implementation.
            </div>
        `;
    }

    function readNodeValue() {
        if (!selectedNodeId) return;
        
        fetch('/api/opcua/read', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ nodeIds: [selectedNodeId] })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                const value = data.data[0];
                document.getElementById('valueContent').innerHTML = `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr><td><strong>Value:</strong></td><td><code>${value.value || 'null'}</code></td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge bg-${value.statusCode.includes('Good') ? 'success' : 'danger'}">${value.statusCode}</span></td></tr>
                            <tr><td><strong>Source Timestamp:</strong></td><td>${new Date(value.sourceTimestamp).toLocaleString()}</td></tr>
                            <tr><td><strong>Server Timestamp:</strong></td><td>${new Date(value.serverTimestamp).toLocaleString()}</td></tr>
                        </table>
                    </div>
                `;
                new bootstrap.Modal(document.getElementById('valueModal')).show();
            } else {
                alert('Failed to read value: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reading value: ' + error.message);
        });
    }

    function writeNodeValue() {
        document.getElementById('writeValueForm').style.display = 'block';
    }

    function executeWrite() {
        if (!selectedNodeId) return;
        
        const value = document.getElementById('writeValue').value;
        const dataType = document.getElementById('writeDataType').value;
        
        let convertedValue = value;
        switch (dataType) {
            case 'number':
                convertedValue = parseFloat(value);
                break;
            case 'boolean':
                convertedValue = value.toLowerCase() === 'true';
                break;
            case 'datetime':
                convertedValue = new Date(value).toISOString();
                break;
        }
        
        fetch('/api/opcua/write', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                writeValues: [{ nodeId: selectedNodeId, value: convertedValue }]
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Value written successfully!');
                cancelWrite();
            } else {
                alert('Failed to write value: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error writing value: ' + error.message);
        });
    }

    function cancelWrite() {
        document.getElementById('writeValueForm').style.display = 'none';
        document.getElementById('writeValue').value = '';
    }

    function subscribeToNode() {
        if (!selectedNodeId) return;
        
        fetch('/api/opcua/subscribe', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                nodeIds: [selectedNodeId],
                publishingInterval: 1000
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Subscription created with ID: ${data.subscriptionId}`);
            } else {
                alert('Failed to create subscription: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error creating subscription: ' + error.message);
        });
    }

    function browseNodeReferences() {
        alert('Reference browsing would be implemented here');
    }

    function expandAll() {
        document.querySelectorAll('.expand-icon.fa-chevron-right').forEach(icon => {
            icon.click();
        });
    }

    function collapseAll() {
        document.querySelectorAll('.expand-icon.fa-chevron-down').forEach(icon => {
            icon.click();
        });
    }

    function filterNodes() {
        const searchTerm = document.getElementById('searchNodes').value.toLowerCase();
        const nodeItems = document.querySelectorAll('.node-item');
        
        nodeItems.forEach(item => {
            const nodeName = item.querySelector('.node-name').textContent.toLowerCase();
            if (nodeName.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
</script>
