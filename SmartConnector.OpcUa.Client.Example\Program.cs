using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Opc.Ua;
using SmartConnector.OpcUa.Client;
using SmartConnector.OpcUa.Client.Extensions;
using SmartConnector.OpcUa.Client.Services;

namespace SmartConnector.OpcUa.Client.Example
{
    /// <summary>
    /// Example program demonstrating SmartConnector OPC UA Client usage
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("SmartConnector OPC UA Client Example");
            Console.WriteLine("====================================");

            // Choose example to run
            Console.WriteLine("Select example to run:");
            Console.WriteLine("1. Basic Client Usage");
            Console.WriteLine("2. Background Service with Subscriptions");
            Console.WriteLine("3. Advanced Operations");
            Console.WriteLine("4. Server Discovery");
            Console.Write("Enter choice (1-4): ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    await RunBasicClientExample();
                    break;
                case "2":
                    await RunBackgroundServiceExample();
                    break;
                case "3":
                    await RunAdvancedOperationsExample();
                    break;
                case "4":
                    await RunServerDiscoveryExample();
                    break;
                default:
                    Console.WriteLine("Invalid choice. Running basic client example...");
                    await RunBasicClientExample();
                    break;
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        /// <summary>
        /// Basic client usage example
        /// </summary>
        static async Task RunBasicClientExample()
        {
            Console.WriteLine("\n=== Basic Client Usage Example ===");

            // Create client configuration
            var config = new OpcUaClientConfiguration
            {
                EndpointUrl = "opc.tcp://localhost:4840",
                ApplicationName = "SmartConnector Example Client",
                ApplicationUri = "urn:SmartConnector:ExampleClient",
                Security = new OpcUaSecurityConfiguration
                {
                    SecurityPolicy = SecurityPolicies.None,
                    MessageSecurityMode = MessageSecurityMode.None,
                    UserIdentityType = UserTokenType.Anonymous
                }
            };

            // Create logger
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            var logger = loggerFactory.CreateLogger<OpcUaClient>();

            // Create and use client
            using var client = new OpcUaClient(config, logger);

            try
            {
                // Connect
                Console.WriteLine("Connecting to OPC UA server...");
                var connectResult = await client.ConnectAsync();
                
                if (!connectResult.IsSuccess)
                {
                    Console.WriteLine($"Connection failed: {connectResult.Message}");
                    return;
                }

                Console.WriteLine("Connected successfully!");

                // Browse root nodes
                Console.WriteLine("\nBrowsing root nodes...");
                var browseResult = await client.BrowseAsync();
                
                if (browseResult.IsSuccess && browseResult.Data != null)
                {
                    foreach (var node in browseResult.Data.Take(10))
                    {
                        Console.WriteLine($"  {node.DisplayName} ({node.NodeClass})");
                    }
                }

                // Read server time
                Console.WriteLine("\nReading server time...");
                var timeResult = await client.ReadAsync(VariableIds.Server_ServerStatus_CurrentTime);
                
                if (timeResult.IsSuccess && timeResult.Data != null)
                {
                    Console.WriteLine($"Server time: {timeResult.Data.Value}");
                }

                // Get server info
                Console.WriteLine("\nGetting server information...");
                var serverInfoResult = await client.GetServerInfoAsync();
                
                if (serverInfoResult.IsSuccess && serverInfoResult.Data != null)
                {
                    var info = serverInfoResult.Data;
                    Console.WriteLine($"Application Name: {info.ApplicationName}");
                    Console.WriteLine($"Product URI: {info.ProductUri}");
                    Console.WriteLine($"Software Version: {info.SoftwareVersion}");
                }

                // Disconnect
                Console.WriteLine("\nDisconnecting...");
                await client.DisconnectAsync();
                Console.WriteLine("Disconnected successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Background service with subscriptions example
        /// </summary>
        static async Task RunBackgroundServiceExample()
        {
            Console.WriteLine("\n=== Background Service with Subscriptions Example ===");

            // Create host with OPC UA client service
            var host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Configure logging
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });

                    // Configure OPC UA client
                    var clientConfig = new OpcUaClientConfiguration
                    {
                        EndpointUrl = "opc.tcp://localhost:4840",
                        ApplicationName = "SmartConnector Service Example",
                        ApplicationUri = "urn:SmartConnector:ServiceExample",
                        Security = new OpcUaSecurityConfiguration
                        {
                            SecurityPolicy = SecurityPolicies.None,
                            MessageSecurityMode = MessageSecurityMode.None,
                            UserIdentityType = UserTokenType.Anonymous
                        }
                    };

                    // Configure service
                    var serviceConfig = new OpcUaClientServiceConfiguration
                    {
                        HealthCheckInterval = 30000,
                        DataSubscriptions = new List<OpcUaDataSubscription>
                        {
                            new OpcUaDataSubscription
                            {
                                Name = "Server Status Subscription",
                                PublishingInterval = 1000,
                                MonitoredItems = new List<OpcUaMonitoredItemConfig>
                                {
                                    new OpcUaMonitoredItemConfig
                                    {
                                        NodeId = VariableIds.Server_ServerStatus_CurrentTime,
                                        DisplayName = "Server Time",
                                        SamplingInterval = 1000
                                    },
                                    new OpcUaMonitoredItemConfig
                                    {
                                        NodeId = VariableIds.Server_ServerStatus_State,
                                        DisplayName = "Server State",
                                        SamplingInterval = 5000
                                    }
                                }
                            }
                        }
                    };

                    // Add OPC UA client services
                    services.AddOpcUaClient(clientConfig, serviceConfig);
                })
                .Build();

            // Get the service and subscribe to events
            var opcUaService = host.Services.GetService<OpcUaClientService>();
            if (opcUaService != null)
            {
                opcUaService.ConnectionChanged += (sender, e) =>
                {
                    Console.WriteLine($"Connection status changed: {(e.IsConnected ? "Connected" : "Disconnected")}");
                    if (!string.IsNullOrEmpty(e.Reason))
                    {
                        Console.WriteLine($"Reason: {e.Reason}");
                    }
                };

                opcUaService.DataChanged += (sender, e) =>
                {
                    Console.WriteLine($"Data changed in subscription {e.SubscriptionId}:");
                    foreach (var item in e.ChangedItems)
                    {
                        Console.WriteLine($"  {item.DisplayName}: {item.Value} (was: {item.PreviousValue})");
                    }
                };
            }

            Console.WriteLine("Starting OPC UA client service...");
            Console.WriteLine("Press Ctrl+C to stop");

            // Run the host
            await host.RunAsync();
        }

        /// <summary>
        /// Advanced operations example
        /// </summary>
        static async Task RunAdvancedOperationsExample()
        {
            Console.WriteLine("\n=== Advanced Operations Example ===");

            var config = new OpcUaClientConfiguration
            {
                EndpointUrl = "opc.tcp://localhost:4840",
                ApplicationName = "SmartConnector Advanced Example",
                ApplicationUri = "urn:SmartConnector:AdvancedExample"
            };

            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            var logger = loggerFactory.CreateLogger<OpcUaClient>();

            using var client = new OpcUaClient(config, logger);

            try
            {
                await client.ConnectAsync();

                // Recursive browse
                Console.WriteLine("Performing recursive browse...");
                var recursiveBrowse = await client.BrowseRecursiveAsync(ObjectIds.ObjectsFolder, 2);
                if (recursiveBrowse.IsSuccess && recursiveBrowse.Data != null)
                {
                    Console.WriteLine($"Found {recursiveBrowse.Data.Count} nodes recursively");
                }

                // Read multiple nodes
                Console.WriteLine("\nReading multiple nodes...");
                var nodeIds = new List<NodeId>
                {
                    VariableIds.Server_ServerStatus_CurrentTime,
                    VariableIds.Server_ServerStatus_State,
                    VariableIds.Server_ServiceLevel
                };

                var readResult = await client.ReadAsync(nodeIds);
                if (readResult.IsSuccess && readResult.Data != null)
                {
                    foreach (var value in readResult.Data)
                    {
                        Console.WriteLine($"  {value.NodeId}: {value.Value} ({value.StatusCode})");
                    }
                }

                // Read node attributes
                Console.WriteLine("\nReading node attributes...");
                var attributesResult = await client.ReadNodeAttributesAsync(VariableIds.Server_ServerStatus_CurrentTime);
                if (attributesResult.IsSuccess && attributesResult.Data != null)
                {
                    var attrs = attributesResult.Data;
                    Console.WriteLine($"  Display Name: {attrs.DisplayName}");
                    Console.WriteLine($"  Data Type: {attrs.DataType}");
                    Console.WriteLine($"  Access Level: {attrs.AccessLevel}");
                }

                // Create subscription with events
                Console.WriteLine("\nCreating event subscription...");
                var eventSubResult = await client.SubscribeToEventsAsync();
                if (eventSubResult.IsSuccess)
                {
                    Console.WriteLine($"Event subscription created with ID: {eventSubResult.Data}");
                    
                    // Wait for events
                    await Task.Delay(5000);
                    
                    // Clean up
                    if (eventSubResult.Data.HasValue)
                    {
                        await client.DeleteSubscriptionAsync(eventSubResult.Data.Value);
                    }
                }

                await client.DisconnectAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Server discovery example
        /// </summary>
        static async Task RunServerDiscoveryExample()
        {
            Console.WriteLine("\n=== Server Discovery Example ===");

            var config = new OpcUaClientConfiguration
            {
                EndpointUrl = "opc.tcp://localhost:4840",
                ApplicationName = "SmartConnector Discovery Example"
            };

            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            var logger = loggerFactory.CreateLogger<OpcUaClient>();

            using var client = new OpcUaClient(config, logger);

            try
            {
                // Find servers
                Console.WriteLine("Finding servers...");
                var serversResult = await client.FindServersAsync("opc.tcp://localhost:4840");
                if (serversResult.IsSuccess && serversResult.Data != null)
                {
                    Console.WriteLine($"Found {serversResult.Data.Count} servers:");
                    foreach (var server in serversResult.Data)
                    {
                        Console.WriteLine($"  {server.ApplicationName} - {server.ApplicationUri}");
                        Console.WriteLine($"    Type: {server.ApplicationType}");
                        if (server.DiscoveryUrls?.Count > 0)
                        {
                            Console.WriteLine($"    Discovery URLs: {string.Join(", ", server.DiscoveryUrls)}");
                        }
                    }
                }

                // Get endpoints
                Console.WriteLine("\nGetting endpoints...");
                var endpointsResult = await client.GetEndpointsAsync();
                if (endpointsResult.IsSuccess && endpointsResult.Data != null)
                {
                    Console.WriteLine($"Found {endpointsResult.Data.Count} endpoints:");
                    foreach (var endpoint in endpointsResult.Data)
                    {
                        Console.WriteLine($"  {endpoint.EndpointUrl}");
                        Console.WriteLine($"    Security: {endpoint.SecurityPolicyUri} / {endpoint.SecurityMode}");
                        Console.WriteLine($"    Transport: {endpoint.TransportProfileUri}");
                    }
                }

                // Find servers on network
                Console.WriteLine("\nFinding servers on network...");
                var networkServersResult = await client.FindServersOnNetworkAsync();
                if (networkServersResult.IsSuccess && networkServersResult.Data != null)
                {
                    Console.WriteLine($"Found {networkServersResult.Data.Count} servers on network:");
                    foreach (var server in networkServersResult.Data)
                    {
                        Console.WriteLine($"  {server.ServerName} - {server.DiscoveryUrl}");
                        Console.WriteLine($"    Capabilities: {string.Join(", ", server.ServerCapabilities ?? new string[0])}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}
