{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "SmartConnector.OpcUa": "Debug"}}, "AllowedHosts": "*", "OpcUa": {"EndpointUrl": "opc.tcp://localhost:4840", "ApplicationName": "SmartConnector OPC UA Web UI", "ApplicationUri": "urn:SmartConnector:OpcUaWebUI", "Security": {"SecurityPolicy": "http://opcfoundation.org/UA/SecurityPolicy#None", "MessageSecurityMode": "None", "UserIdentityType": "Anonymous"}, "Connection": {"ConnectionTimeout": 30000, "OperationTimeout": 30000, "AutoReconnect": true, "ReconnectPeriod": 10000}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/smartconnector-opcua-webui-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}]}}