using Opc.Ua;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// OPC UA operation result
    /// </summary>
    public class OpcUaOperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public StatusCode StatusCode { get; set; } = StatusCodes.Good;
        public string? ErrorDetails { get; set; }
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// Generic OPC UA operation result with data
    /// </summary>
    public class OpcUaOperationResult<T> : OpcUaOperationResult
    {
        public T? Data { get; set; }
    }

    /// <summary>
    /// OPC UA node information
    /// </summary>
    public class OpcUaNode
    {
        public NodeId NodeId { get; set; } = NodeId.Null;
        public string DisplayName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public NodeClass NodeClass { get; set; }
        public string BrowseName { get; set; } = string.Empty;
        public NodeId? DataType { get; set; }
        public int? ValueRank { get; set; }
        public uint[]? ArrayDimensions { get; set; }
        public byte AccessLevel { get; set; }
        public byte UserAccessLevel { get; set; }
        public bool IsAbstract { get; set; }
        public List<OpcUaReference> References { get; set; } = new();
        public List<OpcUaNode> Children { get; set; } = new();
    }

    /// <summary>
    /// OPC UA reference information
    /// </summary>
    public class OpcUaReference
    {
        public NodeId ReferenceTypeId { get; set; } = NodeId.Null;
        public bool IsForward { get; set; }
        public NodeId TargetId { get; set; } = NodeId.Null;
        public string TargetName { get; set; } = string.Empty;
        public NodeClass TargetNodeClass { get; set; }
    }

    /// <summary>
    /// OPC UA value with metadata
    /// </summary>
    public class OpcUaValue
    {
        public NodeId NodeId { get; set; } = NodeId.Null;
        public object? Value { get; set; }
        public StatusCode StatusCode { get; set; } = StatusCodes.Good;
        public DateTime SourceTimestamp { get; set; }
        public DateTime ServerTimestamp { get; set; }
        public string? DisplayName { get; set; }
        public NodeId? DataType { get; set; }
        public int? ValueRank { get; set; }
    }

    /// <summary>
    /// OPC UA write value
    /// </summary>
    public class OpcUaWriteValue
    {
        public NodeId NodeId { get; set; } = NodeId.Null;
        public object? Value { get; set; }
        public uint AttributeId { get; set; } = Attributes.Value;
        public string? IndexRange { get; set; }
    }

    /// <summary>
    /// OPC UA monitored item
    /// </summary>
    public class OpcUaMonitoredItem
    {
        public uint ClientHandle { get; set; }
        public NodeId NodeId { get; set; } = NodeId.Null;
        public uint AttributeId { get; set; } = Attributes.Value;
        public string? IndexRange { get; set; }
        public MonitoringMode MonitoringMode { get; set; } = MonitoringMode.Reporting;
        public double SamplingInterval { get; set; } = 1000;
        public uint QueueSize { get; set; } = 1;
        public bool DiscardOldest { get; set; } = true;
        public MonitoringFilter? Filter { get; set; }
        public string? DisplayName { get; set; }
    }

    /// <summary>
    /// OPC UA subscription settings
    /// </summary>
    public class OpcUaSubscriptionSettings
    {
        public string DisplayName { get; set; } = "SmartConnector Subscription";
        public double PublishingInterval { get; set; } = 1000;
        public uint LifetimeCount { get; set; } = 10000;
        public uint MaxKeepAliveCount { get; set; } = 10;
        public uint MaxNotificationsPerPublish { get; set; } = 1000;
        public bool PublishingEnabled { get; set; } = true;
        public byte Priority { get; set; } = 0;
    }

    /// <summary>
    /// OPC UA server status
    /// </summary>
    public class OpcUaServerStatus
    {
        public DateTime StartTime { get; set; }
        public DateTime CurrentTime { get; set; }
        public ServerState State { get; set; }
        public string BuildInfo { get; set; } = string.Empty;
        public uint SecondsTillShutdown { get; set; }
        public LocalizedText ShutdownReason { get; set; } = LocalizedText.Null;
    }

    /// <summary>
    /// OPC UA server information
    /// </summary>
    public class OpcUaServerInfo
    {
        public string ApplicationName { get; set; } = string.Empty;
        public string ApplicationUri { get; set; } = string.Empty;
        public string ProductUri { get; set; } = string.Empty;
        public ApplicationType ApplicationType { get; set; }
        public string GatewayServerUri { get; set; } = string.Empty;
        public List<string> DiscoveryUrls { get; set; } = new();
        public string SoftwareVersion { get; set; } = string.Empty;
        public string BuildNumber { get; set; } = string.Empty;
        public DateTime BuildDate { get; set; }
        public List<string> ServerCapabilities { get; set; } = new();
        public List<string> SupportedProfiles { get; set; } = new();
    }

    /// <summary>
    /// OPC UA node attributes
    /// </summary>
    public class OpcUaNodeAttributes
    {
        public NodeId NodeId { get; set; } = NodeId.Null;
        public NodeClass NodeClass { get; set; }
        public QualifiedName BrowseName { get; set; } = QualifiedName.Null;
        public LocalizedText DisplayName { get; set; } = LocalizedText.Null;
        public LocalizedText? Description { get; set; }
        public uint WriteMask { get; set; }
        public uint UserWriteMask { get; set; }
        public bool IsAbstract { get; set; }
        public bool Symmetric { get; set; }
        public LocalizedText? InverseName { get; set; }
        public bool ContainsNoLoops { get; set; }
        public byte EventNotifier { get; set; }
        public object? Value { get; set; }
        public NodeId? DataType { get; set; }
        public int ValueRank { get; set; }
        public uint[]? ArrayDimensions { get; set; }
        public byte AccessLevel { get; set; }
        public byte UserAccessLevel { get; set; }
        public double MinimumSamplingInterval { get; set; }
        public bool Historizing { get; set; }
        public bool Executable { get; set; }
        public bool UserExecutable { get; set; }
    }

    /// <summary>
    /// OPC UA history value
    /// </summary>
    public class OpcUaHistoryValue
    {
        public object? Value { get; set; }
        public StatusCode StatusCode { get; set; } = StatusCodes.Good;
        public DateTime SourceTimestamp { get; set; }
        public DateTime ServerTimestamp { get; set; }
    }

    /// <summary>
    /// OPC UA connection event arguments
    /// </summary>
    public class OpcUaConnectionEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string? Reason { get; set; }
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// OPC UA notification event arguments
    /// </summary>
    public class OpcUaNotificationEventArgs : EventArgs
    {
        public uint SubscriptionId { get; set; }
        public List<OpcUaDataChange> DataChanges { get; set; } = new();
    }

    /// <summary>
    /// OPC UA data change notification
    /// </summary>
    public class OpcUaDataChange
    {
        public uint ClientHandle { get; set; }
        public NodeId NodeId { get; set; } = NodeId.Null;
        public object? Value { get; set; }
        public StatusCode StatusCode { get; set; } = StatusCodes.Good;
        public DateTime SourceTimestamp { get; set; }
        public DateTime ServerTimestamp { get; set; }
        public string? DisplayName { get; set; }
    }

    /// <summary>
    /// OPC UA event notification arguments
    /// </summary>
    public class OpcUaEventNotificationArgs : EventArgs
    {
        public uint SubscriptionId { get; set; }
        public List<OpcUaEventField> EventFields { get; set; } = new();
        public NodeId EventType { get; set; } = NodeId.Null;
        public string Message { get; set; } = string.Empty;
        public ushort Severity { get; set; }
        public DateTime Time { get; set; }
    }

    /// <summary>
    /// OPC UA event field
    /// </summary>
    public class OpcUaEventField
    {
        public string Name { get; set; } = string.Empty;
        public object? Value { get; set; }
        public NodeId DataType { get; set; } = NodeId.Null;
    }

    /// <summary>
    /// OPC UA error event arguments
    /// </summary>
    public class OpcUaErrorEventArgs : EventArgs
    {
        public string Message { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public StatusCode StatusCode { get; set; } = StatusCodes.Bad;
    }
}
