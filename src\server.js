const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const logger = require('./utils/logger');
const config = require('./config/config');
const kepwareRoutes = require('./routes/kepware');
const ewsRoutes = require('./routes/ews');
const connectorRoutes = require('./routes/connector');
const SmartConnector = require('./services/SmartConnector');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimitWindow * 60 * 1000, // minutes to milliseconds
  max: config.rateLimitMaxRequests,
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// API key middleware for protected routes
const apiKeyAuth = (req, res, next) => {
  const apiKey = req.header('X-API-Key');
  if (!apiKey || apiKey !== config.apiKey) {
    return res.status(401).json({ error: 'Invalid or missing API key' });
  }
  next();
};

// Routes
app.use('/api/kepware', apiKeyAuth, kepwareRoutes);
app.use('/api/ews', apiKeyAuth, ewsRoutes);
app.use('/api/connector', apiKeyAuth, connectorRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Initialize SmartConnector
const smartConnector = new SmartConnector();

// Make SmartConnector available to routes
app.set('smartConnector', smartConnector);

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await smartConnector.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await smartConnector.stop();
  process.exit(0);
});

const PORT = config.port || 3000;

app.listen(PORT, () => {
  logger.info(`SmartConnector EWS server running on port ${PORT}`);
  smartConnector.start();
});

module.exports = app;
