{"Version": 1, "Hash": "DjbPxfSTYAHh5ndaM29EIAtixvF90vzI0Mx77+vvbkQ=", "Source": "SmartConnector.OpcUa.WebUI", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "SmartConnector.OpcUa.WebUI\\wwwroot", "Source": "SmartConnector.OpcUa.WebUI", "ContentRoot": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "Pattern": "**"}], "Assets": [{"Identity": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\css\\site.css", "SourceId": "SmartConnector.OpcUa.WebUI", "SourceType": "Discovered", "ContentRoot": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "84t6txaswu", "Integrity": "GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 3652, "LastWriteTime": "2025-07-12T16:52:53+00:00"}, {"Identity": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\js\\site.js", "SourceId": "SmartConnector.OpcUa.WebUI", "SourceType": "Discovered", "ContentRoot": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pf8ytgao1u", "Integrity": "NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 7002, "LastWriteTime": "2025-07-12T16:53:21+00:00"}, {"Identity": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "SmartConnector.OpcUa.WebUI", "SourceType": "Discovered", "ContentRoot": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yyoirve2l3", "Integrity": "Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 145, "LastWriteTime": "2025-07-12T17:00:07+00:00"}, {"Identity": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "SmartConnector.OpcUa.WebUI", "SourceType": "Discovered", "ContentRoot": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ijwclhwcxh", "Integrity": "QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 119, "LastWriteTime": "2025-07-12T17:00:14+00:00"}, {"Identity": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "SmartConnector.OpcUa.WebUI", "SourceType": "Discovered", "ContentRoot": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\", "BasePath": "_content/SmartConnector.OpcUa.WebUI", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "23ehigdbv1", "Integrity": "cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 107, "LastWriteTime": "2025-07-12T17:00:22+00:00"}], "Endpoints": [{"Route": "css/site.84t6txaswu.css", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3652"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:52:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "84t6txaswu"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw="}]}, {"Route": "css/site.css", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3652"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:52:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GCaXjrOR9QjJrm4ecj1kOprL4GCxCz013Dmi+VyovGw="}]}, {"Route": "js/site.js", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:53:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A="}]}, {"Route": "js/site.pf8ytgao1u.js", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 16:53:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pf8ytgao1u"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-NXyP4T5ENPk8zqcQCSMRIe7hUBkJssPodj4Ob0Mnt/A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.yyoirve2l3.css", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yyoirve2l3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-Fnq0KWByDMCCeUJMaHOgzXidCyoU+C42lW4PmWP6u/Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.ijwclhwcxh.js", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ijwclhwcxh"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QrdlwOwoL8GA9f0mEe7PO9jjrvakH0TCCfUGwpUWZTw="}]}, {"Route": "lib/jquery/dist/jquery.min.23ehigdbv1.js", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "23ehigdbv1"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "G:\\ua\\client\\SmartConnector.OpcUa.WebUI\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 17:00:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cnyIrWWHzbWrAbQvXZKRv5rHAmk679oiHtbBlEh2RrQ="}]}]}