# Test Environment Configuration
NODE_ENV=test
PORT=3001

# Test Kepware Configuration
KEPWARE_HOST=localhost
KEPWARE_PORT=57412
KEPWARE_USERNAME=test
KEPWARE_PASSWORD=test
KEPWARE_USE_HTTPS=false

# Test EWS Configuration
EWS_URL=https://test.example.com/EWS/Exchange.asmx
EWS_USERNAME=<EMAIL>
EWS_PASSWORD=test
EWS_DOMAIN=test

# Test SmartConnector Configuration
CONNECTOR_POLL_INTERVAL=1000
CONNECTOR_MAX_RETRIES=1
CONNECTOR_TIMEOUT=5000

# Test Logging Configuration
LOG_LEVEL=error
LOG_FILE=logs/test.log

# Test Security
API_KEY=test-api-key
RATE_LIMIT_WINDOW=1
RATE_LIMIT_MAX_REQUESTS=1000
