#!/usr/bin/env node

/**
 * SmartConnector EWS Webserver Startup Script
 * 
 * This script provides a simple way to start the SmartConnector with
 * basic configuration validation and helpful error messages.
 */

const fs = require('fs');
const path = require('path');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  No .env file found. Creating one from .env.example...');
  
  const examplePath = path.join(__dirname, '.env.example');
  if (fs.existsSync(examplePath)) {
    fs.copyFileSync(examplePath, envPath);
    console.log('✅ Created .env file from .env.example');
    console.log('📝 Please edit .env with your configuration before starting the server');
    process.exit(0);
  } else {
    console.error('❌ No .env.example file found. Please create a .env file manually.');
    process.exit(1);
  }
}

// Load environment variables
require('dotenv').config();

// Validate required environment variables
const requiredVars = [
  'KEPWARE_HOST',
  'KEPWARE_USERNAME',
  'KEPWARE_PASSWORD',
  'EWS_URL',
  'EWS_USERNAME',
  'EWS_PASSWORD',
  'API_KEY'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\n📝 Please update your .env file with the missing variables.');
  process.exit(1);
}

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('📁 Created logs directory');
}

console.log('🚀 Starting SmartConnector EWS Webserver...');
console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`🌐 Port: ${process.env.PORT || 3000}`);
console.log(`🏭 Kepware Host: ${process.env.KEPWARE_HOST}:${process.env.KEPWARE_PORT || 57412}`);
console.log(`📧 EWS URL: ${process.env.EWS_URL}`);
console.log('');

// Start the server
try {
  require('./src/server');
} catch (error) {
  console.error('❌ Failed to start server:', error.message);
  process.exit(1);
}
