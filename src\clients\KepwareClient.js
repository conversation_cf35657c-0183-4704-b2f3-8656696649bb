const axios = require('axios');
const logger = require('../utils/logger');

class KepwareClient {
  constructor(config) {
    this.config = config;
    this.baseURL = `${config.useHttps ? 'https' : 'http'}://${config.host}:${config.port}/iotgateway/${config.apiVersion}`;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: config.timeout,
      auth: {
        username: config.username,
        password: config.password
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('Kepware API Request', {
          method: config.method,
          url: config.url,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('Kepware API Request Error', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Kepware API Response', {
          status: response.status,
          url: response.config.url,
          data: response.data
        });
        return response;
      },
      (error) => {
        logger.error('Kepware API Response Error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Test connection to Kepware server
   */
  async testConnection() {
    try {
      const response = await this.client.get('/');
      return {
        success: true,
        message: 'Connection successful',
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        message: `Connection failed: ${error.message}`,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Get all channels
   */
  async getChannels() {
    try {
      const response = await this.client.get('/config/project/channels');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get channels: ${error.message}`);
    }
  }

  /**
   * Get devices for a specific channel
   */
  async getDevices(channelName) {
    try {
      const response = await this.client.get(`/config/project/channels/${channelName}/devices`);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get devices for channel ${channelName}: ${error.message}`);
    }
  }

  /**
   * Get tags for a specific device
   */
  async getTags(channelName, deviceName) {
    try {
      const response = await this.client.get(`/config/project/channels/${channelName}/devices/${deviceName}/tags`);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get tags for device ${deviceName}: ${error.message}`);
    }
  }

  /**
   * Read tag values
   */
  async readTags(tagIds) {
    try {
      const payload = {
        readIds: Array.isArray(tagIds) ? tagIds : [tagIds]
      };
      
      const response = await this.client.post('/read', payload);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to read tags: ${error.message}`);
    }
  }

  /**
   * Write tag values
   */
  async writeTags(writeOperations) {
    try {
      const payload = {
        writeIds: Array.isArray(writeOperations) ? writeOperations : [writeOperations]
      };
      
      const response = await this.client.post('/write', payload);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to write tags: ${error.message}`);
    }
  }

  /**
   * Get project information
   */
  async getProjectInfo() {
    try {
      const response = await this.client.get('/config/project');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get project info: ${error.message}`);
    }
  }

  /**
   * Get server status
   */
  async getServerStatus() {
    try {
      const response = await this.client.get('/status');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get server status: ${error.message}`);
    }
  }

  /**
   * Subscribe to tag changes (if supported)
   */
  async subscribeToTags(tagIds, callback) {
    // Note: This would typically use WebSockets or Server-Sent Events
    // For now, we'll implement polling-based subscription
    const pollInterval = 1000; // 1 second
    
    const poll = async () => {
      try {
        const data = await this.readTags(tagIds);
        callback(null, data);
      } catch (error) {
        callback(error, null);
      }
    };

    const intervalId = setInterval(poll, pollInterval);
    
    // Return unsubscribe function
    return () => {
      clearInterval(intervalId);
    };
  }

  /**
   * Get tag history (if available)
   */
  async getTagHistory(tagId, startTime, endTime) {
    try {
      const params = {
        tagId,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString()
      };
      
      const response = await this.client.get('/history', { params });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get tag history: ${error.message}`);
    }
  }

  /**
   * Validate tag ID format
   */
  validateTagId(tagId) {
    // Basic validation for Kepware tag ID format
    const tagPattern = /^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/;
    return tagPattern.test(tagId);
  }

  /**
   * Build tag ID from channel, device, and tag name
   */
  buildTagId(channelName, deviceName, tagName) {
    return `${channelName}.${deviceName}.${tagName}`;
  }
}

module.exports = KepwareClient;
