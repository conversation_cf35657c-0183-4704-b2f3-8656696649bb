const EWS = require('node-ews');
const logger = require('../utils/logger');

class EWSClient {
  constructor(config) {
    this.config = config;
    this.ews = null;
    this.isConnected = false;
    this.initialize();
  }

  /**
   * Initialize EWS connection
   */
  initialize() {
    try {
      const ewsConfig = {
        username: this.config.username,
        password: this.config.password,
        host: this.extractHostFromUrl(this.config.url),
        auth: 'basic'
      };

      if (this.config.domain) {
        ewsConfig.domain = this.config.domain;
        ewsConfig.auth = 'ntlm';
      }

      this.ews = new EWS(ewsConfig);
      logger.info('EWS client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize EWS client:', error);
      throw error;
    }
  }

  /**
   * Extract host from EWS URL
   */
  extractHostFromUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      throw new Error(`Invalid EWS URL: ${url}`);
    }
  }

  /**
   * Test EWS connection
   */
  async testConnection() {
    try {
      const result = await this.ews.run('GetFolder', {
        FolderShape: {
          BaseShape: 'IdOnly'
        },
        FolderIds: {
          DistinguishedFolderId: {
            Id: 'inbox'
          }
        }
      });

      this.isConnected = true;
      return {
        success: true,
        message: 'EWS connection successful',
        data: result
      };
    } catch (error) {
      this.isConnected = false;
      return {
        success: false,
        message: `EWS connection failed: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Send email
   */
  async sendEmail(emailData) {
    try {
      const { to, cc, bcc, subject, body, isHtml = false, attachments = [] } = emailData;

      const message = {
        Subject: subject,
        Body: {
          BodyType: isHtml ? 'HTML' : 'Text',
          $value: body
        },
        ToRecipients: this.formatRecipients(to),
        Importance: 'Normal'
      };

      if (cc && cc.length > 0) {
        message.CcRecipients = this.formatRecipients(cc);
      }

      if (bcc && bcc.length > 0) {
        message.BccRecipients = this.formatRecipients(bcc);
      }

      if (attachments && attachments.length > 0) {
        message.Attachments = this.formatAttachments(attachments);
      }

      const result = await this.ews.run('CreateItem', {
        MessageDisposition: 'SendAndSaveCopy',
        Items: {
          Message: message
        }
      });

      logger.info('Email sent successfully', { to, subject });
      return {
        success: true,
        message: 'Email sent successfully',
        data: result
      };
    } catch (error) {
      logger.error('Failed to send email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Get inbox messages
   */
  async getInboxMessages(maxItems = 10) {
    try {
      const result = await this.ews.run('FindItem', {
        ItemShape: {
          BaseShape: 'AllProperties'
        },
        IndexedPageItemView: {
          MaxEntriesReturned: maxItems,
          Offset: 0,
          BasePoint: 'Beginning'
        },
        ParentFolderIds: {
          DistinguishedFolderId: {
            Id: 'inbox'
          }
        }
      });

      return result;
    } catch (error) {
      throw new Error(`Failed to get inbox messages: ${error.message}`);
    }
  }

  /**
   * Create calendar appointment
   */
  async createAppointment(appointmentData) {
    try {
      const { subject, body, start, end, location, attendees = [], isAllDay = false } = appointmentData;

      const appointment = {
        Subject: subject,
        Body: {
          BodyType: 'Text',
          $value: body || ''
        },
        Start: start.toISOString(),
        End: end.toISOString(),
        IsAllDayEvent: isAllDay,
        LegacyFreeBusyStatus: 'Busy'
      };

      if (location) {
        appointment.Location = location;
      }

      if (attendees && attendees.length > 0) {
        appointment.RequiredAttendees = this.formatAttendees(attendees);
      }

      const result = await this.ews.run('CreateItem', {
        SendMeetingInvitations: 'SendToAllAndSaveCopy',
        Items: {
          CalendarItem: appointment
        }
      });

      logger.info('Calendar appointment created successfully', { subject, start, end });
      return {
        success: true,
        message: 'Appointment created successfully',
        data: result
      };
    } catch (error) {
      logger.error('Failed to create appointment:', error);
      throw new Error(`Failed to create appointment: ${error.message}`);
    }
  }

  /**
   * Get calendar events
   */
  async getCalendarEvents(startDate, endDate) {
    try {
      const result = await this.ews.run('FindItem', {
        ItemShape: {
          BaseShape: 'AllProperties'
        },
        CalendarView: {
          StartDate: startDate.toISOString(),
          EndDate: endDate.toISOString()
        },
        ParentFolderIds: {
          DistinguishedFolderId: {
            Id: 'calendar'
          }
        }
      });

      return result;
    } catch (error) {
      throw new Error(`Failed to get calendar events: ${error.message}`);
    }
  }

  /**
   * Create task
   */
  async createTask(taskData) {
    try {
      const { subject, body, dueDate, priority = 'Normal', status = 'NotStarted' } = taskData;

      const task = {
        Subject: subject,
        Body: {
          BodyType: 'Text',
          $value: body || ''
        },
        Importance: priority,
        Status: status
      };

      if (dueDate) {
        task.DueDate = dueDate.toISOString();
      }

      const result = await this.ews.run('CreateItem', {
        Items: {
          Task: task
        }
      });

      logger.info('Task created successfully', { subject, dueDate });
      return {
        success: true,
        message: 'Task created successfully',
        data: result
      };
    } catch (error) {
      logger.error('Failed to create task:', error);
      throw new Error(`Failed to create task: ${error.message}`);
    }
  }

  /**
   * Format recipients for EWS
   */
  formatRecipients(recipients) {
    const recipientArray = Array.isArray(recipients) ? recipients : [recipients];
    return {
      Mailbox: recipientArray.map(email => ({
        EmailAddress: email
      }))
    };
  }

  /**
   * Format attendees for calendar appointments
   */
  formatAttendees(attendees) {
    return {
      Attendee: attendees.map(email => ({
        Mailbox: {
          EmailAddress: email
        }
      }))
    };
  }

  /**
   * Format attachments for emails
   */
  formatAttachments(attachments) {
    return {
      FileAttachment: attachments.map(attachment => ({
        Name: attachment.name,
        Content: attachment.content, // Base64 encoded content
        ContentType: attachment.contentType || 'application/octet-stream'
      }))
    };
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      config: {
        url: this.config.url,
        username: this.config.username,
        domain: this.config.domain,
        version: this.config.version
      }
    };
  }
}

module.exports = EWSClient;
