using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace SmartConnector.Ews.Server.Services
{
    /// <summary>
    /// Kepware REST API connector for SmartConnector
    /// </summary>
    public class KepwareConnector : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<KepwareConnector> _logger;
        private readonly KepwareConfiguration _config;
        private bool _isDisposed = false;

        public KepwareConnector(KepwareConfiguration config, ILogger<KepwareConnector> logger, HttpClient? httpClient = null)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _httpClient = httpClient ?? new HttpClient();
            ConfigureHttpClient();
        }

        /// <summary>
        /// Configure HTTP client for Kepware communication
        /// </summary>
        private void ConfigureHttpClient()
        {
            var baseUrl = $"{(_config.UseHttps ? "https" : "http")}://{_config.Host}:{_config.Port}/iotgateway/{_config.ApiVersion}";
            _httpClient.BaseAddress = new Uri(baseUrl);
            _httpClient.Timeout = TimeSpan.FromMilliseconds(_config.TimeoutMs);

            // Set authentication
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_config.Username}:{_config.Password}"));
            _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);
            
            // Set headers
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
        }

        /// <summary>
        /// Test connection to Kepware server
        /// </summary>
        public async Task<KepwareOperationResult> TestConnectionAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Kepware connection test successful");
                    
                    return new KepwareOperationResult
                    {
                        IsSuccess = true,
                        Message = "Connection successful",
                        Data = content
                    };
                }
                else
                {
                    _logger.LogWarning("Kepware connection test failed with status: {StatusCode}", response.StatusCode);
                    
                    return new KepwareOperationResult
                    {
                        IsSuccess = false,
                        Message = $"Connection failed with status: {response.StatusCode}",
                        ErrorDetails = await response.Content.ReadAsStringAsync()
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kepware connection test failed");
                
                return new KepwareOperationResult
                {
                    IsSuccess = false,
                    Message = "Connection failed",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Read tag values from Kepware
        /// </summary>
        public async Task<KepwareOperationResult<List<KepwareTagValue>>> ReadTagsAsync(List<string> tagIds)
        {
            try
            {
                var request = new { readIds = tagIds };
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/read", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<KepwareReadResponse>(responseContent);
                    
                    var tagValues = result?.ReadResults?.Select(r => new KepwareTagValue
                    {
                        TagId = r.Id,
                        Value = r.Value,
                        Quality = r.Status,
                        Timestamp = DateTime.UtcNow
                    }).ToList() ?? new List<KepwareTagValue>();

                    _logger.LogDebug("Successfully read {Count} tags from Kepware", tagValues.Count);
                    
                    return new KepwareOperationResult<List<KepwareTagValue>>
                    {
                        IsSuccess = true,
                        Message = "Tags read successfully",
                        Data = tagValues
                    };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to read tags from Kepware: {StatusCode} - {Error}", response.StatusCode, errorContent);
                    
                    return new KepwareOperationResult<List<KepwareTagValue>>
                    {
                        IsSuccess = false,
                        Message = $"Failed to read tags: {response.StatusCode}",
                        ErrorDetails = errorContent
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while reading tags from Kepware");
                
                return new KepwareOperationResult<List<KepwareTagValue>>
                {
                    IsSuccess = false,
                    Message = "Exception while reading tags",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Write tag values to Kepware
        /// </summary>
        public async Task<KepwareOperationResult> WriteTagsAsync(List<KepwareTagWrite> tagWrites)
        {
            try
            {
                var writeIds = tagWrites.Select(tw => new { id = tw.TagId, v = tw.Value }).ToList();
                var request = new { writeIds };
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/write", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("Successfully wrote {Count} tags to Kepware", tagWrites.Count);
                    
                    return new KepwareOperationResult
                    {
                        IsSuccess = true,
                        Message = "Tags written successfully",
                        Data = responseContent
                    };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to write tags to Kepware: {StatusCode} - {Error}", response.StatusCode, errorContent);
                    
                    return new KepwareOperationResult
                    {
                        IsSuccess = false,
                        Message = $"Failed to write tags: {response.StatusCode}",
                        ErrorDetails = errorContent
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while writing tags to Kepware");
                
                return new KepwareOperationResult
                {
                    IsSuccess = false,
                    Message = "Exception while writing tags",
                    ErrorDetails = ex.Message
                };
            }
        }

        /// <summary>
        /// Get server status from Kepware
        /// </summary>
        public async Task<KepwareOperationResult<KepwareServerStatus>> GetServerStatusAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/status");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var status = JsonConvert.DeserializeObject<KepwareServerStatus>(content);
                    
                    _logger.LogDebug("Retrieved Kepware server status");
                    
                    return new KepwareOperationResult<KepwareServerStatus>
                    {
                        IsSuccess = true,
                        Message = "Server status retrieved successfully",
                        Data = status
                    };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to get server status: {StatusCode} - {Error}", response.StatusCode, errorContent);
                    
                    return new KepwareOperationResult<KepwareServerStatus>
                    {
                        IsSuccess = false,
                        Message = $"Failed to get server status: {response.StatusCode}",
                        ErrorDetails = errorContent
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while getting server status");
                
                return new KepwareOperationResult<KepwareServerStatus>
                {
                    IsSuccess = false,
                    Message = "Exception while getting server status",
                    ErrorDetails = ex.Message
                };
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _httpClient?.Dispose();
                _isDisposed = true;
            }
        }
    }

    /// <summary>
    /// Kepware configuration
    /// </summary>
    public class KepwareConfiguration
    {
        public string Host { get; set; } = "localhost";
        public int Port { get; set; } = 57412;
        public string Username { get; set; } = "Administrator";
        public string Password { get; set; } = string.Empty;
        public bool UseHttps { get; set; } = false;
        public string ApiVersion { get; set; } = "v1";
        public int TimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// Kepware operation result
    /// </summary>
    public class KepwareOperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? ErrorDetails { get; set; }
        public object? Data { get; set; }
    }

    /// <summary>
    /// Generic Kepware operation result
    /// </summary>
    public class KepwareOperationResult<T> : KepwareOperationResult
    {
        public new T? Data { get; set; }
    }

    /// <summary>
    /// Kepware tag value
    /// </summary>
    public class KepwareTagValue
    {
        public string TagId { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Quality { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Kepware tag write operation
    /// </summary>
    public class KepwareTagWrite
    {
        public string TagId { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    /// <summary>
    /// Kepware server status
    /// </summary>
    public class KepwareServerStatus
    {
        public string Status { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public long Uptime { get; set; }
        public int ConnectedClients { get; set; }
    }

    /// <summary>
    /// Kepware read response
    /// </summary>
    public class KepwareReadResponse
    {
        public List<KepwareReadResult>? ReadResults { get; set; }
    }

    /// <summary>
    /// Kepware read result
    /// </summary>
    public class KepwareReadResult
    {
        public string Id { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
