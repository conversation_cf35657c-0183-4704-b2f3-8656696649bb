using Opc.Ua;

namespace SmartConnector.OpcUa.Client
{
    /// <summary>
    /// OPC UA client configuration
    /// </summary>
    public class OpcUaClientConfiguration
    {
        /// <summary>
        /// Server endpoint URL
        /// </summary>
        public string EndpointUrl { get; set; } = "opc.tcp://localhost:4840";

        /// <summary>
        /// Application name
        /// </summary>
        public string ApplicationName { get; set; } = "SmartConnector OPC UA Client";

        /// <summary>
        /// Application URI
        /// </summary>
        public string ApplicationUri { get; set; } = "urn:SmartConnector:OpcUaClient";

        /// <summary>
        /// Product URI
        /// </summary>
        public string ProductUri { get; set; } = "https://smartconnector.com/opcua";

        /// <summary>
        /// Application type
        /// </summary>
        public ApplicationType ApplicationType { get; set; } = ApplicationType.Client;

        /// <summary>
        /// Security configuration
        /// </summary>
        public OpcUaSecurityConfiguration Security { get; set; } = new();

        /// <summary>
        /// Session configuration
        /// </summary>
        public OpcUaSessionConfiguration Session { get; set; } = new();

        /// <summary>
        /// Connection configuration
        /// </summary>
        public OpcUaConnectionConfiguration Connection { get; set; } = new();

        /// <summary>
        /// Subscription configuration
        /// </summary>
        public OpcUaSubscriptionConfiguration Subscription { get; set; } = new();

        /// <summary>
        /// Certificate store configuration
        /// </summary>
        public OpcUaCertificateConfiguration Certificate { get; set; } = new();

        /// <summary>
        /// Logging configuration
        /// </summary>
        public OpcUaLoggingConfiguration Logging { get; set; } = new();
    }

    /// <summary>
    /// OPC UA security configuration
    /// </summary>
    public class OpcUaSecurityConfiguration
    {
        /// <summary>
        /// Security policy
        /// </summary>
        public string SecurityPolicy { get; set; } = SecurityPolicies.None;

        /// <summary>
        /// Message security mode
        /// </summary>
        public MessageSecurityMode MessageSecurityMode { get; set; } = MessageSecurityMode.None;

        /// <summary>
        /// User identity type
        /// </summary>
        public UserTokenType UserIdentityType { get; set; } = UserTokenType.Anonymous;

        /// <summary>
        /// Username for username/password authentication
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// Password for username/password authentication
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// Certificate file path for certificate authentication
        /// </summary>
        public string? CertificateFilePath { get; set; }

        /// <summary>
        /// Private key file path for certificate authentication
        /// </summary>
        public string? PrivateKeyFilePath { get; set; }

        /// <summary>
        /// Certificate password
        /// </summary>
        public string? CertificatePassword { get; set; }

        /// <summary>
        /// Auto accept untrusted certificates
        /// </summary>
        public bool AutoAcceptUntrustedCertificates { get; set; } = false;

        /// <summary>
        /// Reject SHA1 signed certificates
        /// </summary>
        public bool RejectSHA1SignedCertificates { get; set; } = true;

        /// <summary>
        /// Minimum certificate key size
        /// </summary>
        public ushort MinimumCertificateKeySize { get; set; } = 2048;
    }

    /// <summary>
    /// OPC UA session configuration
    /// </summary>
    public class OpcUaSessionConfiguration
    {
        /// <summary>
        /// Session name
        /// </summary>
        public string SessionName { get; set; } = "SmartConnector Session";

        /// <summary>
        /// Session timeout in milliseconds
        /// </summary>
        public uint SessionTimeout { get; set; } = 60000;

        /// <summary>
        /// Max request message size
        /// </summary>
        public uint MaxRequestMessageSize { get; set; } = 4 * 1024 * 1024; // 4MB

        /// <summary>
        /// Max response message size
        /// </summary>
        public uint MaxResponseMessageSize { get; set; } = 4 * 1024 * 1024; // 4MB

        /// <summary>
        /// Max array length
        /// </summary>
        public uint MaxArrayLength { get; set; } = 65535;

        /// <summary>
        /// Max string length
        /// </summary>
        public uint MaxStringLength { get; set; } = 65535;

        /// <summary>
        /// Max byte string length
        /// </summary>
        public uint MaxByteStringLength { get; set; } = 4 * 1024 * 1024; // 4MB

        /// <summary>
        /// Locale IDs
        /// </summary>
        public List<string> LocaleIds { get; set; } = new() { "en-US" };
    }

    /// <summary>
    /// OPC UA connection configuration
    /// </summary>
    public class OpcUaConnectionConfiguration
    {
        /// <summary>
        /// Connection timeout in milliseconds
        /// </summary>
        public int ConnectionTimeout { get; set; } = 30000;

        /// <summary>
        /// Operation timeout in milliseconds
        /// </summary>
        public int OperationTimeout { get; set; } = 30000;

        /// <summary>
        /// Keep alive interval in milliseconds
        /// </summary>
        public uint KeepAliveInterval { get; set; } = 5000;

        /// <summary>
        /// Auto reconnect enabled
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// Reconnect period in milliseconds
        /// </summary>
        public int ReconnectPeriod { get; set; } = 10000;

        /// <summary>
        /// Max reconnect attempts (0 = infinite)
        /// </summary>
        public int MaxReconnectAttempts { get; set; } = 0;

        /// <summary>
        /// Use reverse connect
        /// </summary>
        public bool UseReverseConnect { get; set; } = false;

        /// <summary>
        /// Reverse connect port
        /// </summary>
        public int ReverseConnectPort { get; set; } = 4840;
    }

    /// <summary>
    /// OPC UA subscription configuration
    /// </summary>
    public class OpcUaSubscriptionConfiguration
    {
        /// <summary>
        /// Default publishing interval in milliseconds
        /// </summary>
        public double DefaultPublishingInterval { get; set; } = 1000;

        /// <summary>
        /// Default lifetime count
        /// </summary>
        public uint DefaultLifetimeCount { get; set; } = 10000;

        /// <summary>
        /// Default max keep alive count
        /// </summary>
        public uint DefaultMaxKeepAliveCount { get; set; } = 10;

        /// <summary>
        /// Default max notifications per publish
        /// </summary>
        public uint DefaultMaxNotificationsPerPublish { get; set; } = 1000;

        /// <summary>
        /// Default priority
        /// </summary>
        public byte DefaultPriority { get; set; } = 0;

        /// <summary>
        /// Default sampling interval in milliseconds
        /// </summary>
        public double DefaultSamplingInterval { get; set; } = 1000;

        /// <summary>
        /// Default queue size
        /// </summary>
        public uint DefaultQueueSize { get; set; } = 1;

        /// <summary>
        /// Default discard oldest
        /// </summary>
        public bool DefaultDiscardOldest { get; set; } = true;
    }

    /// <summary>
    /// OPC UA certificate configuration
    /// </summary>
    public class OpcUaCertificateConfiguration
    {
        /// <summary>
        /// Application certificate store type
        /// </summary>
        public string ApplicationCertificateStoreType { get; set; } = "Directory";

        /// <summary>
        /// Application certificate store path
        /// </summary>
        public string ApplicationCertificateStorePath { get; set; } = "OPC Foundation/CertificateStores/MachineDefault";

        /// <summary>
        /// Trusted issuer certificate store type
        /// </summary>
        public string TrustedIssuerCertificateStoreType { get; set; } = "Directory";

        /// <summary>
        /// Trusted issuer certificate store path
        /// </summary>
        public string TrustedIssuerCertificateStorePath { get; set; } = "OPC Foundation/CertificateStores/UA Certificate Authorities";

        /// <summary>
        /// Trusted peer certificate store type
        /// </summary>
        public string TrustedPeerCertificateStoreType { get; set; } = "Directory";

        /// <summary>
        /// Trusted peer certificate store path
        /// </summary>
        public string TrustedPeerCertificateStorePath { get; set; } = "OPC Foundation/CertificateStores/UA Applications";

        /// <summary>
        /// Rejected certificate store type
        /// </summary>
        public string RejectedCertificateStoreType { get; set; } = "Directory";

        /// <summary>
        /// Rejected certificate store path
        /// </summary>
        public string RejectedCertificateStorePath { get; set; } = "OPC Foundation/CertificateStores/RejectedCertificates";

        /// <summary>
        /// Auto create certificate
        /// </summary>
        public bool AutoCreateCertificate { get; set; } = true;

        /// <summary>
        /// Certificate subject name
        /// </summary>
        public string CertificateSubjectName { get; set; } = "CN=SmartConnector OPC UA Client, O=SmartConnector, DC=localhost";

        /// <summary>
        /// Certificate key size
        /// </summary>
        public ushort CertificateKeySize { get; set; } = 2048;

        /// <summary>
        /// Certificate lifetime in months
        /// </summary>
        public ushort CertificateLifetime { get; set; } = 60;
    }

    /// <summary>
    /// OPC UA logging configuration
    /// </summary>
    public class OpcUaLoggingConfiguration
    {
        /// <summary>
        /// Enable trace logging
        /// </summary>
        public bool EnableTrace { get; set; } = false;

        /// <summary>
        /// Trace file path
        /// </summary>
        public string? TraceFilePath { get; set; }

        /// <summary>
        /// Trace masks
        /// </summary>
        public int TraceMasks { get; set; } = 0;

        /// <summary>
        /// Max trace file size in MB
        /// </summary>
        public int MaxTraceFileSize { get; set; } = 10;

        /// <summary>
        /// Max trace files
        /// </summary>
        public int MaxTraceFiles { get; set; } = 5;
    }
}
