const request = require('supertest');
const app = require('../src/server');
const KepwareClient = require('../src/clients/KepwareClient');

// Mock KepwareClient
jest.mock('../src/clients/KepwareClient');

describe('Kepware API Endpoints', () => {
  const apiKey = process.env.API_KEY || 'test-api-key';
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/kepware/test', () => {
    it('should test Kepware connection successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Connection successful',
        data: { version: '6.0' }
      };

      KepwareClient.prototype.testConnection = jest.fn().mockResolvedValue(mockResponse);

      const response = await request(app)
        .get('/api/kepware/test')
        .set('X-API-Key', apiKey)
        .expect(200);

      expect(response.body).toEqual(mockResponse);
      expect(KepwareClient.prototype.testConnection).toHaveBeenCalled();
    });

    it('should handle connection failure', async () => {
      const mockError = new Error('Connection failed');
      KepwareClient.prototype.testConnection = jest.fn().mockRejectedValue(mockError);

      const response = await request(app)
        .get('/api/kepware/test')
        .set('X-API-Key', apiKey)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Connection test failed');
    });

    it('should require API key', async () => {
      await request(app)
        .get('/api/kepware/test')
        .expect(401);
    });
  });

  describe('GET /api/kepware/status', () => {
    it('should get server status', async () => {
      const mockStatus = {
        status: 'Running',
        uptime: 12345,
        version: '6.0'
      };

      KepwareClient.prototype.getServerStatus = jest.fn().mockResolvedValue(mockStatus);

      const response = await request(app)
        .get('/api/kepware/status')
        .set('X-API-Key', apiKey)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockStatus);
    });
  });

  describe('GET /api/kepware/channels', () => {
    it('should get all channels', async () => {
      const mockChannels = [
        { name: 'Channel1', type: 'Simulator' },
        { name: 'Channel2', type: 'OPC UA' }
      ];

      KepwareClient.prototype.getChannels = jest.fn().mockResolvedValue(mockChannels);

      const response = await request(app)
        .get('/api/kepware/channels')
        .set('X-API-Key', apiKey)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockChannels);
    });
  });

  describe('POST /api/kepware/read', () => {
    it('should read tag values', async () => {
      const tagIds = ['Channel1.Device1.Tag1', 'Channel1.Device1.Tag2'];
      const mockResponse = {
        readResults: [
          { id: 'Channel1.Device1.Tag1', v: 100, s: 'GOOD' },
          { id: 'Channel1.Device1.Tag2', v: 200, s: 'GOOD' }
        ]
      };

      KepwareClient.prototype.readTags = jest.fn().mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/kepware/read')
        .set('X-API-Key', apiKey)
        .send({ tagIds })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockResponse);
      expect(KepwareClient.prototype.readTags).toHaveBeenCalledWith(tagIds);
    });

    it('should validate tagIds parameter', async () => {
      const response = await request(app)
        .post('/api/kepware/read')
        .set('X-API-Key', apiKey)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('tagIds array is required');
    });

    it('should validate tagIds is array', async () => {
      const response = await request(app)
        .post('/api/kepware/read')
        .set('X-API-Key', apiKey)
        .send({ tagIds: 'not-an-array' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('tagIds array is required');
    });
  });

  describe('POST /api/kepware/write', () => {
    it('should write tag values', async () => {
      const writeOperations = [
        { id: 'Channel1.Device1.Tag1', v: 150 },
        { id: 'Channel1.Device1.Tag2', v: 250 }
      ];
      const mockResponse = {
        writeResults: [
          { id: 'Channel1.Device1.Tag1', s: 'GOOD' },
          { id: 'Channel1.Device1.Tag2', s: 'GOOD' }
        ]
      };

      KepwareClient.prototype.writeTags = jest.fn().mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/kepware/write')
        .set('X-API-Key', apiKey)
        .send({ writeOperations })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockResponse);
      expect(KepwareClient.prototype.writeTags).toHaveBeenCalledWith(writeOperations);
    });

    it('should validate writeOperations parameter', async () => {
      const response = await request(app)
        .post('/api/kepware/write')
        .set('X-API-Key', apiKey)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('writeOperations array is required');
    });
  });

  describe('GET /api/kepware/history/:tagId', () => {
    it('should get tag history', async () => {
      const tagId = 'Channel1.Device1.Tag1';
      const startTime = '2024-01-01T00:00:00Z';
      const endTime = '2024-01-01T23:59:59Z';
      const mockHistory = {
        tagId,
        values: [
          { timestamp: '2024-01-01T12:00:00Z', value: 100 },
          { timestamp: '2024-01-01T13:00:00Z', value: 110 }
        ]
      };

      KepwareClient.prototype.getTagHistory = jest.fn().mockResolvedValue(mockHistory);

      const response = await request(app)
        .get(`/api/kepware/history/${tagId}`)
        .query({ startTime, endTime })
        .set('X-API-Key', apiKey)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockHistory);
      expect(KepwareClient.prototype.getTagHistory).toHaveBeenCalledWith(
        tagId,
        new Date(startTime),
        new Date(endTime)
      );
    });

    it('should validate date parameters', async () => {
      const tagId = 'Channel1.Device1.Tag1';

      const response = await request(app)
        .get(`/api/kepware/history/${tagId}`)
        .set('X-API-Key', apiKey)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('startTime and endTime query parameters are required');
    });

    it('should validate date format', async () => {
      const tagId = 'Channel1.Device1.Tag1';

      const response = await request(app)
        .get(`/api/kepware/history/${tagId}`)
        .query({ startTime: 'invalid-date', endTime: 'invalid-date' })
        .set('X-API-Key', apiKey)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid date format for startTime or endTime');
    });
  });
});
