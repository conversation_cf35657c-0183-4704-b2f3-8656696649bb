const logger = require('./logger');

/**
 * Custom error classes for different types of errors
 */
class SmartConnectorError extends Error {
  constructor(message, code = 'SMARTCONNECTOR_ERROR', statusCode = 500) {
    super(message);
    this.name = 'SmartConnectorError';
    this.code = code;
    this.statusCode = statusCode;
    this.timestamp = new Date().toISOString();
  }
}

class KepwareError extends SmartConnectorError {
  constructor(message, statusCode = 500) {
    super(message, 'KEPWARE_ERROR', statusCode);
    this.name = 'KepwareError';
  }
}

class EWSError extends SmartConnectorError {
  constructor(message, statusCode = 500) {
    super(message, 'EWS_ERROR', statusCode);
    this.name = 'EWSError';
  }
}

class ConfigurationError extends SmartConnectorError {
  constructor(message, statusCode = 400) {
    super(message, 'CONFIGURATION_ERROR', statusCode);
    this.name = 'ConfigurationError';
  }
}

class ValidationError extends SmartConnectorError {
  constructor(message, statusCode = 400) {
    super(message, 'VALIDATION_ERROR', statusCode);
    this.name = 'ValidationError';
  }
}

/**
 * Error handler utility class
 */
class ErrorHandler {
  /**
   * Handle and log errors appropriately
   * @param {Error} error - Error to handle
   * @param {string} context - Context where error occurred
   * @param {object} metadata - Additional metadata
   */
  static handle(error, context = 'Unknown', metadata = {}) {
    const errorInfo = {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      context,
      timestamp: new Date().toISOString(),
      stack: error.stack,
      ...metadata
    };

    // Log based on error severity
    if (error.statusCode >= 500 || !error.statusCode) {
      logger.error('Server Error', errorInfo);
    } else if (error.statusCode >= 400) {
      logger.warn('Client Error', errorInfo);
    } else {
      logger.info('Handled Error', errorInfo);
    }

    return errorInfo;
  }

  /**
   * Wrap async functions with error handling
   * @param {Function} fn - Async function to wrap
   * @param {string} context - Context for error handling
   */
  static wrapAsync(fn, context) {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handle(error, context);
        throw error;
      }
    };
  }

  /**
   * Create Express error middleware
   */
  static expressMiddleware() {
    return (error, req, res, next) => {
      const errorInfo = this.handle(error, `${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.body,
        query: req.query,
        params: req.params
      });

      // Don't expose internal errors in production
      const isProduction = process.env.NODE_ENV === 'production';
      const response = {
        success: false,
        error: {
          message: isProduction && error.statusCode >= 500 
            ? 'Internal server error' 
            : error.message,
          code: error.code || 'UNKNOWN_ERROR',
          timestamp: errorInfo.timestamp
        }
      };

      // Add stack trace in development
      if (!isProduction && error.stack) {
        response.error.stack = error.stack;
      }

      res.status(error.statusCode || 500).json(response);
    };
  }

  /**
   * Retry function with exponential backoff
   * @param {Function} fn - Function to retry
   * @param {object} options - Retry options
   */
  static async retry(fn, options = {}) {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 30000,
      exponentialBackoff = true,
      retryCondition = () => true
    } = options;

    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries || !retryCondition(error)) {
          break;
        }

        const delay = exponentialBackoff 
          ? Math.min(baseDelay * Math.pow(2, attempt), maxDelay)
          : baseDelay;

        logger.warn(`Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms`, {
          error: error.message,
          attempt: attempt + 1,
          delay
        });

        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Sleep utility
   * @param {number} ms - Milliseconds to sleep
   */
  static sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Circuit breaker pattern implementation
   */
  static createCircuitBreaker(fn, options = {}) {
    const {
      failureThreshold = 5,
      resetTimeout = 60000,
      monitoringPeriod = 60000
    } = options;

    let state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    let failureCount = 0;
    let lastFailureTime = null;
    let successCount = 0;

    return async (...args) => {
      if (state === 'OPEN') {
        if (Date.now() - lastFailureTime >= resetTimeout) {
          state = 'HALF_OPEN';
          successCount = 0;
          logger.info('Circuit breaker transitioning to HALF_OPEN');
        } else {
          throw new SmartConnectorError('Circuit breaker is OPEN', 'CIRCUIT_BREAKER_OPEN', 503);
        }
      }

      try {
        const result = await fn(...args);
        
        if (state === 'HALF_OPEN') {
          successCount++;
          if (successCount >= 3) {
            state = 'CLOSED';
            failureCount = 0;
            logger.info('Circuit breaker transitioning to CLOSED');
          }
        } else if (state === 'CLOSED') {
          failureCount = 0;
        }

        return result;
      } catch (error) {
        failureCount++;
        lastFailureTime = Date.now();

        if (state === 'HALF_OPEN' || failureCount >= failureThreshold) {
          state = 'OPEN';
          logger.warn('Circuit breaker transitioning to OPEN', {
            failureCount,
            threshold: failureThreshold
          });
        }

        throw error;
      }
    };
  }

  /**
   * Health check utility
   * @param {Array} checks - Array of health check functions
   */
  static async healthCheck(checks = []) {
    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {}
    };

    for (const check of checks) {
      try {
        const result = await Promise.race([
          check.fn(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), check.timeout || 5000)
          )
        ]);

        results.checks[check.name] = {
          status: 'healthy',
          result,
          duration: Date.now() - new Date(results.timestamp).getTime()
        };
      } catch (error) {
        results.checks[check.name] = {
          status: 'unhealthy',
          error: error.message,
          duration: Date.now() - new Date(results.timestamp).getTime()
        };
        results.status = 'unhealthy';
      }
    }

    return results;
  }
}

module.exports = {
  ErrorHandler,
  SmartConnectorError,
  KepwareError,
  EWSError,
  ConfigurationError,
  ValidationError
};
