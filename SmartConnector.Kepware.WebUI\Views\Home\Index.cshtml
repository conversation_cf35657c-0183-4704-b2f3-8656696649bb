@model SmartConnector.Kepware.WebUI.Models.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt"></i>
        Kepware Dashboard
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-@(Model.IsConnected ? "success" : "danger") fs-6">
                <span class="status-indicator status-@(Model.IsConnected ? "connected" : "disconnected")"></span>
                @Model.ConnectionStatus
            </span>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.ErrorMessage))
{
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle"></i>
        @Model.ErrorMessage
    </div>
}

<!-- Connection Status Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plug"></i>
                    Connection Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 mb-2">
                                <i class="fas fa-@(Model.IsConnected ? "check-circle text-success" : "times-circle text-danger")"></i>
                            </div>
                            <h6>Connection</h6>
                            <p class="text-muted">@Model.ConnectionStatus</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 mb-2">
                                <i class="fas fa-tags text-info"></i>
                            </div>
                            <h6>Subscribed Tags</h6>
                            <p class="text-muted">@Model.SubscribedTagsCount</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 mb-2">
                                <i class="fas fa-clock text-warning"></i>
                            </div>
                            <h6>Last Update</h6>
                            <p class="text-muted">
                                @if (Model.LastUpdateTime != DateTime.MinValue)
                                {
                                    @Model.LastUpdateTime.ToString("HH:mm:ss")
                                }
                                else
                                {
                                    <span>Never</span>
                                }
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="display-6 mb-2">
                                <i class="fas fa-server text-primary"></i>
                            </div>
                            <h6>Server Status</h6>
                            <p class="text-muted">
                                @if (Model.ServerStatus != null)
                                {
                                    @Model.ServerStatus.Status
                                }
                                else
                                {
                                    <span>Unknown</span>
                                }
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="readSampleTags()">
                                <i class="fas fa-download"></i>
                                Read Sample Tags
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <button class="btn btn-success" onclick="subscribeTags()">
                                <i class="fas fa-bell"></i>
                                Subscribe to Updates
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="/Home/Tags" class="btn btn-info">
                                <i class="fas fa-search"></i>
                                Browse Tags
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="/Home/Settings" class="btn btn-secondary">
                                <i class="fas fa-cog"></i>
                                Configure
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Tag Values -->
@if (Model.RecentTagValues != null && Model.RecentTagValues.Any())
{
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i>
                        Recent Tag Values
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Tag ID</th>
                                    <th>Value</th>
                                    <th>Quality</th>
                                    <th>Timestamp</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var tag in Model.RecentTagValues)
                                {
                                    <tr>
                                        <td><code>@tag.TagId</code></td>
                                        <td><strong>@tag.Value</strong></td>
                                        <td>
                                            <span class="badge bg-@(tag.Quality == "Good" ? "success" : "warning")">
                                                @tag.Quality
                                            </span>
                                        </td>
                                        <td>@tag.Timestamp.ToString("HH:mm:ss")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        // SignalR connection
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/kepwareHub")
            .build();

        connection.start().then(function () {
            console.log("Connected to Kepware Hub");
        }).catch(function (err) {
            console.error("Error connecting to hub: " + err.toString());
        });

        // Handle connection status updates
        connection.on("ConnectionStatusChanged", function (status) {
            console.log("Connection status changed:", status);
            // Refresh page to show updated status
            setTimeout(() => location.reload(), 1000);
        });

        // Handle tag value updates
        connection.on("TagValuesUpdated", function (tagValues) {
            console.log("Tag values updated:", tagValues);
            // Update the recent values table
            updateTagValuesTable(tagValues);
        });

        function readSampleTags() {
            const sampleTags = [
                "Channel1.Device1.Tag1",
                "Simulation.Ramp.R0",
                "Simulation.Sine.S0"
            ];
            
            connection.invoke("ReadTags", sampleTags).catch(function (err) {
                console.error("Error reading tags: " + err.toString());
            });
        }

        function subscribeTags() {
            const sampleTags = [
                "Channel1.Device1.Tag1",
                "Simulation.Ramp.R0",
                "Simulation.Sine.S0"
            ];
            
            connection.invoke("SubscribeToTags", sampleTags).catch(function (err) {
                console.error("Error subscribing to tags: " + err.toString());
            });
        }

        function updateTagValuesTable(tagValues) {
            // Implementation to update the table with new values
            console.log("Updating tag values table with:", tagValues);
        }
    </script>
}
