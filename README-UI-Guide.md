# SmartConnector OPC UA Client - UI Guide

This guide shows you how to see and use the different user interfaces available for the SmartConnector OPC UA Client.

## 🎯 **Available UI Options**

### 1. 🌐 **Web Dashboard (Recommended)**
A modern, responsive web-based dashboard with real-time updates.

### 2. 💻 **Console Application**
An interactive command-line interface with rich text formatting.

### 3. 🖥️ **Desktop Application** (Optional)
WPF/WinUI desktop application (can be created if needed).

---

## 🌐 **Option 1: Web Dashboard**

### **Features:**
- **Real-time Dashboard** with connection status and server information
- **Interactive Node Browser** with tree view and search
- **Live Data Monitoring** with charts and graphs
- **Settings Panel** for connection configuration
- **SignalR Integration** for real-time updates
- **Responsive Design** works on desktop, tablet, and mobile

### **How to Run:**

1. **Build and Run the Web Application:**
   ```bash
   cd SmartConnector.OpcUa.WebUI
   dotnet run
   ```

2. **Open Your Browser:**
   ```
   http://localhost:5000
   ```
   or
   ```
   https://localhost:5001
   ```

3. **What You'll See:**

   **Dashboard Page:**
   ```
   ┌─────────────────────────────────────────────────────────────┐
   │ 🔗 SmartConnector OPC UA                    🟢 Connected    │
   ├─────────────────────────────────────────────────────────────┤
   │ 📊 Dashboard  🌳 Node Browser  📈 Monitor  ⚙️ Settings     │
   ├─────────────────────────────────────────────────────────────┤
   │                                                             │
   │ Connection Status: ✅ Connected                             │
   │ Server: opc.tcp://localhost:4840                           │
   │                                                             │
   │ ┌─────────────────┐  ┌─────────────────┐                  │
   │ │ Server Info     │  │ Recent Values   │                  │
   │ │ • App Name      │  │ • Server Time   │                  │
   │ │ • Version       │  │ • Server State  │                  │
   │ │ • Build Info    │  │ • Service Level │                  │
   │ └─────────────────┘  └─────────────────┘                  │
   │                                                             │
   │ Quick Actions: [Browse] [Monitor] [Read Time] [Status]     │
   └─────────────────────────────────────────────────────────────┘
   ```

   **Node Browser Page:**
   ```
   ┌─────────────────────────────────────────────────────────────┐
   │ 🌳 Node Browser                                             │
   ├─────────────────────────────────────────────────────────────┤
   │ Address Space          │ Node Details                       │
   │ ┌─────────────────────┐│ ┌─────────────────────────────────┐│
   │ │ 🔍 Search...        ││ │ Selected Node Info              ││
   │ │                     ││ │ • Node ID: ns=0;i=85            ││
   │ │ ▼ Objects           ││ │ • Display Name: Objects         ││
   │ │   ▼ Server          ││ │ • Node Class: Object            ││
   │ │     • ServerStatus  ││ │ • Description: ...              ││
   │ │     • Namespaces    ││ └─────────────────────────────────┘│
   │ │   ▼ DeviceSet       ││                                    │
   │ │ ▼ Types             ││ Node Operations                    │
   │ │ ▼ Views             ││ [Read] [Write] [Subscribe] [Refs]  │
   │ └─────────────────────┘│                                    │
   └─────────────────────────────────────────────────────────────┘
   ```

### **Key Features:**
- **Real-time Updates**: Data changes appear instantly via SignalR
- **Interactive Tree**: Click to expand/collapse nodes
- **Search Functionality**: Find nodes quickly
- **Read/Write Operations**: Direct interaction with OPC UA nodes
- **Subscription Management**: Monitor data changes in real-time

---

## 💻 **Option 2: Console Application**

### **Features:**
- **Interactive Menus** with arrow key navigation
- **Rich Text Formatting** with colors and tables
- **Real-time Data Display** in console
- **Cross-platform** (Windows, Linux, macOS)

### **How to Run:**

1. **Build and Run:**
   ```bash
   cd SmartConnector.OpcUa.ConsoleUI
   dotnet run
   ```

2. **What You'll See:**

   **Welcome Screen:**
   ```
   ═══════════════════════════════════════════════════════════════
                SmartConnector OPC UA Console Client
   ═══════════════════════════════════════════════════════════════

   ┌─────────────────── Getting Started ───────────────────────┐
   │                                                            │
   │ Welcome to SmartConnector OPC UA Console Client!          │
   │                                                            │
   │ This interactive console application allows you to:        │
   │ • Connect to OPC UA servers                               │
   │ • Browse the server address space                         │
   │ • Read and write node values                              │
   │ • Monitor real-time data changes                          │
   │ • Discover servers on the network                         │
   │                                                            │
   │ Use the arrow keys to navigate menus and press Enter.     │
   └────────────────────────────────────────────────────────────┘

   Press any key to continue...
   ```

   **Main Menu:**
   ```
   Connection Status: 🔴 Disconnected

   What would you like to do?
   ❯ Connect to Server
     Disconnect from Server
     Browse Address Space
     Read Node Values
     Write Node Values
     Monitor Data Changes
     Server Discovery
     Server Information
     Settings
     Exit
   ```

   **Browse Results:**
   ```
   ┌─────────────────┬─────────────┬──────────────────────────┐
   │ Display Name    │ Node Class  │ Node ID                  │
   ├─────────────────┼─────────────┼──────────────────────────┤
   │ Objects         │ Object      │ i=85                     │
   │ Types           │ Object      │ i=86                     │
   │ Views           │ Object      │ i=87                     │
   │ Server          │ Object      │ i=2253                   │
   │ ServerStatus    │ Variable    │ i=2256                   │
   └─────────────────┴─────────────┴──────────────────────────┘
   ```

### **Navigation:**
- **Arrow Keys**: Navigate menu options
- **Enter**: Select option
- **Type Values**: When prompted for input
- **Any Key**: Continue after viewing results

---

## 🖥️ **Option 3: Desktop Application** (Optional)

If you need a desktop application, I can create a WPF or WinUI version with:

### **Features Would Include:**
- **Native Windows Look & Feel**
- **Drag & Drop Node Operations**
- **Tabbed Interface** for multiple connections
- **Charts and Graphs** for data visualization
- **System Tray Integration**
- **Offline Mode** with cached data

### **To Request Desktop UI:**
Let me know if you'd like me to create a desktop application version!

---

## 🚀 **Quick Start Guide**

### **1. Start with Web UI (Easiest):**
```bash
# Terminal 1: Start an OPC UA test server (if you have one)
# Or use any existing OPC UA server

# Terminal 2: Start the Web UI
cd SmartConnector.OpcUa.WebUI
dotnet run

# Open browser to: http://localhost:5000
```

### **2. Try Console UI:**
```bash
cd SmartConnector.OpcUa.ConsoleUI
dotnet run
```

### **3. Test with Demo Server:**
If you don't have an OPC UA server, you can:

1. **Download OPC Expert or similar** for testing
2. **Use online demo servers:**
   - `opc.tcp://opcuaserver.com:48010`
   - `opc.tcp://milo.digitalpetri.com:62541/milo`
3. **Run a local simulator**

---

## 📱 **Mobile Access**

The Web UI is responsive and works on mobile devices:

- **Tablet View**: Full functionality with touch navigation
- **Phone View**: Optimized layout for small screens
- **Touch Gestures**: Tap to expand nodes, swipe for navigation

---

## 🔧 **Customization**

### **Web UI Customization:**
- **Themes**: Modify CSS in `wwwroot/css/site.css`
- **Layout**: Edit Razor views in `Views/`
- **Real-time Updates**: Customize SignalR hub in `Hubs/OpcUaHub.cs`

### **Console UI Customization:**
- **Colors**: Modify Spectre.Console markup
- **Menus**: Add new options in `Program.cs`
- **Display**: Customize table layouts and formatting

---

## 🐛 **Troubleshooting**

### **Common Issues:**

1. **"Connection Failed"**
   - Check OPC UA server is running
   - Verify endpoint URL is correct
   - Check firewall settings

2. **"Port Already in Use" (Web UI)**
   - Change port in `appsettings.json`
   - Or use: `dotnet run --urls="http://localhost:5002"`

3. **"Certificate Errors"**
   - Set `AutoAcceptUntrustedCertificates = true` in config
   - Or properly configure certificates

### **Getting Help:**
- Check logs in `logs/` directory
- Enable debug logging in `appsettings.json`
- Use browser developer tools for web UI issues

---

## 🎉 **Next Steps**

1. **Start with the Web UI** - it's the most feature-complete
2. **Connect to your OPC UA server**
3. **Browse the address space** to understand your data
4. **Set up monitoring** for critical values
5. **Customize the interface** for your specific needs

The SmartConnector OPC UA Client provides multiple ways to interact with your industrial data - choose the interface that best fits your workflow!
