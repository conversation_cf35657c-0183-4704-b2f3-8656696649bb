# SmartConnector.Ews.Server.dll

A .NET library that provides Exchange Web Services (EWS) server functionality for SmartConnector integration with Kepware and Microsoft Exchange.

## Overview

This library is similar to `Mongoose.Ews.Server.dll` and provides a complete .NET solution for:
- Connecting to Microsoft Exchange Server or Office 365
- Integrating with Kepware REST API
- Automated email notifications based on industrial data
- Calendar appointment scheduling
- Task management
- Real-time tag monitoring and rule execution

## Features

### 🔧 **Core Components**
- **EwsServer**: Main EWS client for Exchange operations
- **KepwareConnector**: REST API client for Kepware communication
- **SmartConnectorService**: Background service that bridges both systems
- **Dependency Injection**: Full .NET Core DI container support

### 📧 **Exchange Web Services**
- Send emails with attachments
- Create calendar appointments with attendees
- Create and manage tasks
- Support for both Exchange Server and Office 365
- NTLM and Basic authentication
- Connection testing and server information

### 🏭 **Kepware Integration**
- Read/write tag values via REST API
- Server status monitoring
- Connection testing
- Configurable polling intervals
- Error handling and retry logic

### 🧠 **Smart Rules Engine**
- Tag change-based rules
- Scheduled rules (future enhancement)
- Configurable conditions and actions
- Email notifications on alarms
- Automatic maintenance scheduling

## Installation

### NuGet Package
```bash
Install-Package SmartConnector.Ews.Server
```

### Manual Build
```bash
git clone <repository>
cd SmartConnector.Ews.Server
dotnet build
dotnet pack
```

## Quick Start

### 1. Basic Setup

```csharp
using SmartConnector.Ews.Server;
using SmartConnector.Ews.Server.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

// Configure services
var host = Host.CreateDefaultBuilder()
    .ConfigureServices((context, services) =>
    {
        // Configure EWS
        var ewsConfig = new EwsConfiguration
        {
            ExchangeUrl = "https://outlook.office365.com/EWS/Exchange.asmx",
            Username = "<EMAIL>",
            Password = "your-password",
            ExchangeVersion = ExchangeVersion.Exchange2016
        };

        // Configure Kepware
        var kepwareConfig = new KepwareConfiguration
        {
            Host = "localhost",
            Port = 57412,
            Username = "Administrator",
            Password = "your-kepware-password"
        };

        // Add SmartConnector services
        services.AddSmartConnectorEws(ewsConfig, kepwareConfig);
    })
    .Build();

// Run the service
await host.RunAsync();
```

### 2. Direct Usage

```csharp
// Create EWS server
var ewsConfig = new EwsConfiguration
{
    ExchangeUrl = "https://outlook.office365.com/EWS/Exchange.asmx",
    Username = "<EMAIL>",
    Password = "password"
};

var logger = serviceProvider.GetService<ILogger<EwsServer>>();
var ewsServer = new EwsServer(ewsConfig, logger);

// Test connection
var connectionResult = await ewsServer.TestConnectionAsync();
if (connectionResult.IsSuccess)
{
    Console.WriteLine("Connected to Exchange successfully!");
}

// Send email
var emailResult = await ewsServer.SendEmailAsync(new EwsEmailMessage
{
    To = new List<string> { "<EMAIL>" },
    Subject = "Alert from SmartConnector",
    Body = "Equipment alarm triggered!",
    Priority = EwsImportance.High
});

// Create appointment
var appointmentResult = await ewsServer.CreateAppointmentAsync(new EwsAppointment
{
    Subject = "Maintenance Required",
    Start = DateTime.Now.AddDays(1),
    End = DateTime.Now.AddDays(1).AddHours(1),
    RequiredAttendees = new List<string> { "<EMAIL>" }
});
```

### 3. Kepware Integration

```csharp
// Create Kepware connector
var kepwareConfig = new KepwareConfiguration
{
    Host = "localhost",
    Port = 57412,
    Username = "Administrator",
    Password = "password"
};

var kepwareLogger = serviceProvider.GetService<ILogger<KepwareConnector>>();
var kepwareConnector = new KepwareConnector(kepwareConfig, kepwareLogger);

// Test connection
var kepwareTest = await kepwareConnector.TestConnectionAsync();

// Read tags
var tagResult = await kepwareConnector.ReadTagsAsync(new List<string>
{
    "Channel1.Device1.Temperature",
    "Channel1.Device1.Pressure"
});

if (tagResult.IsSuccess && tagResult.Data != null)
{
    foreach (var tag in tagResult.Data)
    {
        Console.WriteLine($"Tag: {tag.TagId} = {tag.Value} ({tag.Quality})");
    }
}
```

## Configuration

### EWS Configuration
```csharp
public class EwsConfiguration
{
    public string ExchangeUrl { get; set; }           // Exchange server URL
    public string Username { get; set; }             // Username
    public string Password { get; set; }             // Password
    public string? Domain { get; set; }              // Domain (for NTLM)
    public ExchangeVersion ExchangeVersion { get; set; } = ExchangeVersion.Exchange2016;
    public bool UseDefaultCredentials { get; set; }  // Use Windows credentials
    public bool EnableTracing { get; set; }          // Enable EWS tracing
    public string? TraceFilePath { get; set; }       // Trace file path
    public int TimeoutMinutes { get; set; } = 5;     // Request timeout
    public bool AcceptInvalidCertificates { get; set; } // Accept invalid SSL certificates
}
```

### Kepware Configuration
```csharp
public class KepwareConfiguration
{
    public string Host { get; set; } = "localhost";  // Kepware server host
    public int Port { get; set; } = 57412;           // Kepware REST API port
    public string Username { get; set; }             // Username
    public string Password { get; set; }             // Password
    public bool UseHttps { get; set; } = false;      // Use HTTPS
    public string ApiVersion { get; set; } = "v1";   // API version
    public int TimeoutMs { get; set; } = 30000;      // Request timeout
}
```

## API Reference

### IEwsServer Interface

```csharp
public interface IEwsServer
{
    Task<EwsConnectionResult> TestConnectionAsync();
    Task<EwsOperationResult> SendEmailAsync(EwsEmailMessage message);
    Task<EwsOperationResult> CreateAppointmentAsync(EwsAppointment appointment);
    Task<EwsOperationResult> CreateTaskAsync(EwsTask task);
    Task<EwsOperationResult<List<EwsEmailMessage>>> GetInboxMessagesAsync(int maxItems = 10);
    Task<EwsOperationResult<List<EwsAppointment>>> GetCalendarEventsAsync(DateTime startDate, DateTime endDate);
}
```

### KepwareConnector Methods

```csharp
public class KepwareConnector
{
    Task<KepwareOperationResult> TestConnectionAsync();
    Task<KepwareOperationResult<List<KepwareTagValue>>> ReadTagsAsync(List<string> tagIds);
    Task<KepwareOperationResult> WriteTagsAsync(List<KepwareTagWrite> tagWrites);
    Task<KepwareOperationResult<KepwareServerStatus>> GetServerStatusAsync();
}
```

## Example Use Cases

### 1. Alarm Notification System
```csharp
// Monitor alarm tag and send email when triggered
var tagResult = await kepwareConnector.ReadTagsAsync(new List<string> { "Channel1.Device1.AlarmStatus" });

if (tagResult.IsSuccess && tagResult.Data?.FirstOrDefault()?.Value is bool alarmActive && alarmActive)
{
    await ewsServer.SendEmailAsync(new EwsEmailMessage
    {
        To = new List<string> { "<EMAIL>" },
        Subject = "CRITICAL ALARM: Equipment Failure",
        Body = $"Alarm triggered at {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
        Priority = EwsImportance.High
    });
}
```

### 2. Maintenance Scheduling
```csharp
// Schedule maintenance when required
var maintenanceRequired = await kepwareConnector.ReadTagsAsync(new List<string> { "Channel1.Device1.MaintenanceRequired" });

if (maintenanceRequired.IsSuccess && (bool)maintenanceRequired.Data?.FirstOrDefault()?.Value)
{
    var tomorrow = DateTime.Now.AddDays(1).Date.AddHours(9);
    
    await ewsServer.CreateAppointmentAsync(new EwsAppointment
    {
        Subject = "Equipment Maintenance",
        Start = tomorrow,
        End = tomorrow.AddHours(2),
        Location = "Production Floor",
        RequiredAttendees = new List<string> { "<EMAIL>", "<EMAIL>" }
    });
}
```

## Building the DLL

To build the library as a DLL:

```bash
cd SmartConnector.Ews.Server
dotnet build -c Release
```

The compiled DLL will be available in:
`SmartConnector.Ews.Server/bin/Release/net6.0/SmartConnector.Ews.Server.dll`

## Dependencies

- Microsoft.Exchange.WebServices (2.2.0)
- Microsoft.Extensions.Hosting (7.0.1)
- Microsoft.Extensions.Logging (7.0.0)
- Microsoft.Extensions.DependencyInjection (7.0.0)
- Newtonsoft.Json (13.0.3)

## License

MIT License - see LICENSE file for details.

## Support

This library provides a complete .NET solution similar to Mongoose.Ews.Server.dll for integrating Kepware industrial automation systems with Microsoft Exchange Web Services.
