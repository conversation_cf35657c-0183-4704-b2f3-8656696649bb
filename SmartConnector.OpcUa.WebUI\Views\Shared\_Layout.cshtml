<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - SmartConnector OPC UA Client</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        .connection-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .node-tree {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .node-item {
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
        }
        
        .node-item:hover {
            background-color: #f8f9fa;
        }
        
        .value-card {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-good { background-color: #28a745; }
        .status-bad { background-color: #dc3545; }
        .status-uncertain { background-color: #ffc107; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                <i class="fas fa-network-wired"></i> SmartConnector OPC UA
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                <ul class="navbar-nav flex-grow-1">
                    <li class="nav-item">
                        <span class="navbar-text" id="connection-indicator">
                            <span class="status-indicator status-bad"></span>
                            <span id="connection-text">Disconnected</span>
                        </span>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text" id="last-update">
                            Last Update: <span id="update-time">--</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="sidebar-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")" 
                               asp-controller="Home" asp-action="Index">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Browser" ? "active" : "")" 
                               asp-controller="Home" asp-action="Browser">
                                <i class="fas fa-sitemap"></i> Node Browser
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Monitor" ? "active" : "")" 
                               asp-controller="Home" asp-action="Monitor">
                                <i class="fas fa-chart-line"></i> Real-time Monitor
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Action"]?.ToString() == "Settings" ? "active" : "")" 
                               asp-controller="Home" asp-action="Settings">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Quick Actions</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <button class="btn btn-sm btn-outline-success w-100 mb-2" onclick="connectToServer()">
                                <i class="fas fa-plug"></i> Connect
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="btn btn-sm btn-outline-danger w-100 mb-2" onclick="disconnectFromServer()">
                                <i class="fas fa-unlink"></i> Disconnect
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="btn btn-sm btn-outline-info w-100 mb-2" onclick="refreshData()">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </li>
                    </ul>
                </div>
            </nav>

            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 main-content">
                @RenderBody()
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    
    <script>
        // SignalR connection
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/opcuahub")
            .build();

        connection.start().then(function () {
            console.log("SignalR connected");
        }).catch(function (err) {
            console.error("SignalR connection error: " + err.toString());
        });

        // Handle connection status updates
        connection.on("ConnectionStatusChanged", function (isConnected, reason) {
            updateConnectionStatus(isConnected, reason);
        });

        // Handle data updates
        connection.on("DataChanged", function (data) {
            updateDataDisplay(data);
        });

        function updateConnectionStatus(isConnected, reason) {
            const indicator = document.getElementById('connection-indicator');
            const text = document.getElementById('connection-text');
            const statusDot = indicator.querySelector('.status-indicator');
            
            if (isConnected) {
                statusDot.className = 'status-indicator status-good';
                text.textContent = 'Connected';
            } else {
                statusDot.className = 'status-indicator status-bad';
                text.textContent = 'Disconnected';
            }
            
            updateLastUpdateTime();
        }

        function updateLastUpdateTime() {
            document.getElementById('update-time').textContent = new Date().toLocaleTimeString();
        }

        function connectToServer() {
            const endpointUrl = prompt("Enter OPC UA Server URL:", "opc.tcp://localhost:4840");
            if (endpointUrl) {
                fetch('/api/opcua/connect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ endpointUrl: endpointUrl })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Connected successfully!');
                        location.reload();
                    } else {
                        alert('Connection failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Connection error: ' + error.message);
                });
            }
        }

        function disconnectFromServer() {
            fetch('/api/opcua/disconnect', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Disconnected successfully!');
                    location.reload();
                } else {
                    alert('Disconnect failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Disconnect error: ' + error.message);
            });
        }

        function refreshData() {
            location.reload();
        }

        // Update connection status on page load
        fetch('/api/opcua/status')
            .then(response => response.json())
            .then(data => {
                updateConnectionStatus(data.isConnected);
            })
            .catch(error => console.error('Error getting status:', error));
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
