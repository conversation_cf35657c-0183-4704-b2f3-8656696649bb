@{
    ViewData["Title"] = "Real-time Monitor";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-chart-line"></i> Real-time Monitor</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="startMonitoring()">
                <i class="fas fa-play"></i> Start
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="stopMonitoring()">
                <i class="fas fa-stop"></i> Stop
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearData()">
                <i class="fas fa-trash"></i> Clear
            </button>
        </div>
    </div>
</div>

<!-- Monitoring Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info" id="monitoring-status">
            <i class="fas fa-info-circle"></i>
            <strong>Monitoring Status:</strong> <span id="status-text">Ready to start monitoring</span>
        </div>
    </div>
</div>

<!-- Real-time Data Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-thermometer-half"></i> Temperature</h5>
            </div>
            <div class="card-body text-center">
                <h2 class="text-primary" id="temperature-value">--°C</h2>
                <small class="text-muted">Last Update: <span id="temperature-time">--</span></small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-tachometer-alt"></i> Pressure</h5>
            </div>
            <div class="card-body text-center">
                <h2 class="text-success" id="pressure-value">-- Pa</h2>
                <small class="text-muted">Last Update: <span id="pressure-time">--</span></small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-tint"></i> Flow Rate</h5>
            </div>
            <div class="card-body text-center">
                <h2 class="text-warning" id="flowrate-value">-- L/min</h2>
                <small class="text-muted">Last Update: <span id="flowrate-time">--</span></small>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-table"></i> Live Data Stream</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-sm" id="data-table">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Node ID</th>
                                <th>Display Name</th>
                                <th>Value</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="data-table-body">
                            <tr>
                                <td colspan="5" class="text-center text-muted">No data available. Start monitoring to see live updates.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart Placeholder -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-chart-area"></i> Trend Chart</h5>
            </div>
            <div class="card-body">
                <div id="trend-chart" style="height: 300px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa; border: 2px dashed #dee2e6;">
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <p>Real-time trend chart will be displayed here</p>
                        <small>Chart implementation ready for integration</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Management -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-bell"></i> Active Subscriptions</h5>
            </div>
            <div class="card-body">
                <div id="subscriptions-list">
                    <div class="alert alert-secondary">
                        <i class="fas fa-info-circle"></i>
                        No active subscriptions. Subscriptions will be created automatically when monitoring starts.
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="createCustomSubscription()">
                        <i class="fas fa-plus"></i> Create Custom Subscription
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let isMonitoring = false;
    let dataCount = 0;
    const maxDataRows = 100;

    // SignalR connection (already established in layout)
    if (typeof connection !== 'undefined') {
        connection.on("DataChanged", function (dataChanges) {
            if (isMonitoring) {
                updateRealTimeData(dataChanges);
            }
        });
    }

    function startMonitoring() {
        isMonitoring = true;
        document.getElementById('status-text').textContent = 'Monitoring active - receiving real-time updates';
        document.getElementById('monitoring-status').className = 'alert alert-success';
        
        // Clear existing data
        clearDataTable();
        
        console.log('Monitoring started');
    }

    function stopMonitoring() {
        isMonitoring = false;
        document.getElementById('status-text').textContent = 'Monitoring stopped';
        document.getElementById('monitoring-status').className = 'alert alert-warning';
        
        console.log('Monitoring stopped');
    }

    function clearData() {
        clearDataTable();
        
        // Reset value displays
        document.getElementById('temperature-value').textContent = '--°C';
        document.getElementById('pressure-value').textContent = '-- Pa';
        document.getElementById('flowrate-value').textContent = '-- L/min';
        
        document.getElementById('temperature-time').textContent = '--';
        document.getElementById('pressure-time').textContent = '--';
        document.getElementById('flowrate-time').textContent = '--';
        
        dataCount = 0;
        console.log('Data cleared');
    }

    function clearDataTable() {
        const tbody = document.getElementById('data-table-body');
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No data available. Start monitoring to see live updates.</td></tr>';
    }

    function updateRealTimeData(dataChanges) {
        const tbody = document.getElementById('data-table-body');
        
        // Remove "no data" message if it exists
        if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
            tbody.innerHTML = '';
        }
        
        dataChanges.forEach(change => {
            // Update value cards
            updateValueCard(change);
            
            // Add to data table
            addDataRow(change);
        });
        
        // Limit table rows
        while (tbody.children.length > maxDataRows) {
            tbody.removeChild(tbody.lastChild);
        }
    }

    function updateValueCard(change) {
        const now = new Date().toLocaleTimeString();
        
        if (change.NodeId.includes('Temperature')) {
            document.getElementById('temperature-value').textContent = change.Value;
            document.getElementById('temperature-time').textContent = now;
        } else if (change.NodeId.includes('Pressure')) {
            document.getElementById('pressure-value').textContent = change.Value;
            document.getElementById('pressure-time').textContent = now;
        } else if (change.NodeId.includes('FlowRate')) {
            document.getElementById('flowrate-value').textContent = change.Value;
            document.getElementById('flowrate-time').textContent = now;
        }
    }

    function addDataRow(change) {
        const tbody = document.getElementById('data-table-body');
        const row = document.createElement('tr');
        
        const statusClass = change.StatusCode === 'Good' ? 'text-success' : 'text-danger';
        
        row.innerHTML = `
            <td>${new Date().toLocaleTimeString()}</td>
            <td><small>${change.NodeId}</small></td>
            <td>${change.DisplayName || 'Unknown'}</td>
            <td><strong>${change.Value}</strong></td>
            <td><span class="${statusClass}">${change.StatusCode}</span></td>
        `;
        
        // Add animation
        row.style.backgroundColor = '#fff3cd';
        tbody.insertBefore(row, tbody.firstChild);
        
        // Remove highlight after animation
        setTimeout(() => {
            row.style.backgroundColor = '';
        }, 1000);
        
        dataCount++;
    }

    function createCustomSubscription() {
        const nodeId = prompt('Enter Node ID to subscribe to:', 'ns=2;s=CustomNode');
        if (nodeId) {
            fetch('/api/opcua/subscribe', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    nodeIds: [nodeId],
                    publishingInterval: 1000
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Subscription created successfully! ID: ${data.subscriptionId}`);
                } else {
                    alert('Failed to create subscription: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error creating subscription');
            });
        }
    }

    // Auto-start monitoring when page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Monitor page loaded');
        // Uncomment to auto-start monitoring
        // setTimeout(startMonitoring, 1000);
    });
</script>
